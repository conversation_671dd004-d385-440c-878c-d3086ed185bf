{"name": "grello", "private": true, "version": "2.0.0-beta.1", "type": "module", "scripts": {"dev": "next dev --port 3010", "build": "next build", "start": "next start -p 3010", "db:migrate": "dotenv -e .env.local -- prisma migrate dev --schema ./orm/schema.prisma", "db:gen": "dotenv -e .env.local -- prisma generate --schema ./orm/schema.prisma", "lint": "biome format . --write && biome check ."}, "dependencies": {"@hello-pangea/dnd": "^16.5.0", "@mui/joy": "^5.0.0-beta.52", "@prisma/client": "6.0.1", "@tanstack/react-query": "^5.85.1", "@trpc/client": "^11.4.4", "@trpc/react-query": "^11.4.4", "@trpc/server": "^11.4.4", "agentlib": "workspace:*", "basenext": "workspace:*", "lucide-react": "0.542.0", "next": "15.3.3", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "^6.26.0", "sharelib": "workspace:*", "zod": "^3.25.0"}, "devDependencies": {"@types/node": "^20.11.24", "prisma": "6.0.1", "typescript": "^5.8.3"}}