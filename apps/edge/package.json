{"name": "@bika/edge", "private": true, "version": "2.0.0-beta.1", "type": "module", "description": "Vercel Serverless functions for edge computing", "scripts": {"start": "dotenv -e ../../apps/web/.env.local -- vercel dev --listen 3666", "deploy": "vercel --prod"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/lodash": "^4.14.202", "@vercel/edge-config": "^1.1.0", "axios": "^1.10.0", "grammy": "^1.22.4", "lodash": "^4.17.21", "node-fetch": "^3.3.2"}, "devDependencies": {"@vercel/node": "^3.0.28"}}