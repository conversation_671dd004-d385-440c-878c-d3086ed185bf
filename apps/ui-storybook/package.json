{"name": "@bika/ui-storybook", "private": true, "version": "2.0.0-beta.1", "type": "module", "scripts": {"_dev": "vite", "dev": "dotenv -e ../web/.env.local -- storybook dev -p 6006", "_build": "tsc && vite build", "build": "NODE_OPTIONS=--max-old-space-size=10240  storybook build", "lint": "biome format . --write && biome check .", "preview": "vite preview", "serve-storybook": "dotenv -e ../web/.env.local -- serve storybook-static"}, "dependencies": {"@bika/api-caller": "workspace:*", "@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "autoprefixer": "^10.4.16", "basenext": "workspace:*", "postcss": "^8.4.40", "react": "18.3.1", "react-dom": "18.3.1", "toolsdk": "workspace:*"}, "devDependencies": {"@chromatic-com/storybook": "^3", "@storybook/addon-essentials": "8.6.14", "@storybook/addon-interactions": "8.6.14", "@storybook/addon-links": "8.6.14", "@storybook/addon-onboarding": "8.6.14", "@storybook/addon-postcss": "^2.0.0", "@storybook/blocks": "8.6.14", "@storybook/react": "8.6.14", "@storybook/react-vite": "8.6.14", "@storybook/test": "8.6.14", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react-swc": "^3.11.0", "globals": "^15.12.0", "storybook": "8.6.14", "storybook-addon-root-attributes": "latest", "typescript": "^5.8.3", "vite": "^5.4.19"}}