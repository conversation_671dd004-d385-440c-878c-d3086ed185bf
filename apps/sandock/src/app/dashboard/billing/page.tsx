import { auth } from '@clerk/nextjs/server';
import Box from '@mui/joy/Box';
import Card from '@mui/joy/Card';
import Chip from '@mui/joy/Chip';
import Divider from '@mui/joy/Divider';
import Grid from '@mui/joy/Grid';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import Table from '@mui/joy/Table';
import Typography from '@mui/joy/Typography';
import type { BillingPlanId } from '@sandock/domains/billing/types';
import { prisma } from '@sandock/domains/prisma';
import { getOrCreateUserSpace } from '@sandock/domains/sandbox/space';
import { ensureUserByClerkId } from '../../../lib/ensureUser';
import { PricingPlans } from './components/PricingPlans';

// Billing aggregation concept (improved):
// runtimeMs = accumulatedRunMs (closed segments) + active segment (if RUNNING)
// billableMs = ceil(runtimeMs / 60_000) * 60_000 (minute rounding)
// Cost = (billableHours) * (cpuLimit * CPU_RATE + memoryGB * MEM_RATE + storageGB * STORAGE_RATE)
// NOTE: memoryLimit assumed MB -> convert to GB.

const CPU_RATE = 0.05; // per vCPU hour USD (example)
const MEM_RATE = 0.01; // per GB hour USD
const STORAGE_RATE = 0.0005; // per GB hour USD (placeholder)

// (hoursBetween removed; direct ms math used)

export default async function BillingPage() {
  const { userId } = await auth();
  if (!userId) {
    return (
      <Sheet variant="soft" sx={{ p: 3, borderRadius: 'sm' }}>
        <Typography level="body-md">请先登录。</Typography>
      </Sheet>
    );
  }
  const user = await ensureUserByClerkId(userId);
  if (!user) {
    return (
      <Sheet variant="soft" color="danger" sx={{ p: 3, borderRadius: 'sm' }}>
        <Typography level="body-md">用户同步失败，请刷新或重试。</Typography>
      </Sheet>
    );
  }

  const space = await getOrCreateUserSpace(user.id);
  const sandboxes = await prisma.sandbox.findMany({
    where: { spaceId: space.id, deletedAt: null },
    orderBy: { createdAt: 'desc' },
    select: {
      id: true,
      title: true,
      provider: true,
      status: true,
      cpuLimit: true,
      memoryLimit: true,
      createdAt: true,
      lastStartedAt: true,
      accumulatedRunMs: true,
    },
  });

  const now = new Date();
  const nowMs = now.getTime();
  const rows = sandboxes.map((s) => {
    const accumulated = s.accumulatedRunMs || 0;
    const activeExtra =
      s.status === 'RUNNING' && s.lastStartedAt ? nowMs - s.lastStartedAt.getTime() : 0;
    const runtimeMs = accumulated + activeExtra;
    const billableMs = Math.ceil(runtimeMs / 60_000) * 60_000;
    const runtimeHours = runtimeMs / 3_600_000;
    const billableHours = billableMs / 3_600_000;
    const cpu = s.cpuLimit || 0; // vCPU
    const memGB = (s.memoryLimit || 0) / 1024; // memoryLimit assumed MB
    const storageGB = 0; // placeholder
    const ratePerHour = cpu * CPU_RATE + memGB * MEM_RATE + storageGB * STORAGE_RATE;
    const cost = billableHours * ratePerHour;
    return { sandbox: s, runtimeHours, billableHours, cpu, memGB, storageGB, cost, ratePerHour };
  });

  const totalCost = rows.reduce((acc, r) => acc + r.cost, 0);
  const totalRuntime = rows.reduce((acc, r) => acc + r.runtimeHours, 0);
  const totalBillable = rows.reduce((acc, r) => acc + r.billableHours, 0);
  const totalCPU = rows.reduce((acc, r) => acc + r.cpu, 0);
  const totalMem = rows.reduce((acc, r) => acc + r.memGB, 0);

  // Pricing derived metrics (per hour / per month conversions)

  // unitPricing retained for potential future summary usage (currently handled inside PricingPlans component)

  // Fetch billing plan
  const billing = await prisma.spaceBilling.findUnique({ where: { spaceId: space.id } });
  const currentPlan: BillingPlanId = (billing?.plan as BillingPlanId) || 'HOBBY';

  return (
    <Stack spacing={4}>
      <Typography level="h3" sx={{ fontWeight: 600 }}>
        Billing Overview
      </Typography>
      <Grid container spacing={2} columns={12}>
        <Grid xs={12} sm={6} md={3}>
          <Card variant="outlined" sx={{ p: 2 }}>
            <Typography level="title-sm">当前估算费用</Typography>
            <Typography level="h4" sx={{ mt: 0.5 }}>
              ${totalCost.toFixed(2)}
            </Typography>
            <Chip size="sm" variant="soft" color="primary" sx={{ mt: 1 }}>
              实时估算
            </Chip>
          </Card>
        </Grid>
        <Grid xs={12} sm={6} md={3}>
          <Card variant="outlined" sx={{ p: 2 }}>
            <Typography level="title-sm">总运行小时(实际)</Typography>
            <Typography level="h4" sx={{ mt: 0.5 }}>
              {totalRuntime.toFixed(1)}
            </Typography>
          </Card>
        </Grid>
        <Grid xs={12} sm={6} md={3}>
          <Card variant="outlined" sx={{ p: 2 }}>
            <Typography level="title-sm">Total Billable Hours</Typography>
            <Typography level="h4" sx={{ mt: 0.5 }}>
              {totalBillable.toFixed(1)}
            </Typography>
          </Card>
        </Grid>
        <Grid xs={12} sm={6} md={3}>
          <Card variant="outlined" sx={{ p: 2 }}>
            <Typography level="title-sm">累计 vCPU</Typography>
            <Typography level="h4" sx={{ mt: 0.5 }}>
              {totalCPU}
            </Typography>
          </Card>
        </Grid>
        <Grid xs={12} sm={6} md={3}>
          <Card variant="outlined" sx={{ p: 2 }}>
            <Typography level="title-sm">累计内存(GB)</Typography>
            <Typography level="h4" sx={{ mt: 0.5 }}>
              {totalMem.toFixed(1)}
            </Typography>
          </Card>
        </Grid>
      </Grid>

      <PricingPlans currentPlan={currentPlan} />

      <Card variant="outlined" sx={{ p: 2 }}>
        <Typography level="title-sm" sx={{ mb: 1 }}>
          Sandbox Usage (running sandboxes update in real-time)
        </Typography>
        <Table size="sm" borderAxis="bothBetween" hoverRow>
          <thead>
            <tr>
              <th>Sandbox</th>
              <th>Provider</th>
              <th>Status</th>
              <th>vCPU</th>
              <th>Memory (GB)</th>
              <th>Runtime Hours</th>
              <th>Billable Hours</th>
              <th>Rate ($/h)</th>
              <th>Est. Cost ($)</th>
            </tr>
          </thead>
          <tbody>
            {rows.map((r) => (
              <tr key={r.sandbox.id}>
                <td>{r.sandbox.title}</td>
                <td>{r.sandbox.provider}</td>
                <td>{r.sandbox.status}</td>
                <td>{r.cpu}</td>
                <td>{r.memGB.toFixed(2)}</td>
                <td>{r.runtimeHours.toFixed(2)}</td>
                <td>{r.billableHours.toFixed(2)}</td>
                <td>{r.ratePerHour.toFixed(4)}</td>
                <td>{r.cost.toFixed(4)}</td>
              </tr>
            ))}
            {rows.length === 0 && (
              <tr>
                <td colSpan={9} style={{ textAlign: 'center', padding: 16 }}>
                  No data
                </td>
              </tr>
            )}
          </tbody>
        </Table>
        <Divider sx={{ my: 2 }} />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
            Notes: Runtime = accumulatedRunMs + active segment. Billing rounds up to the nearest
            minute.
          </Typography>
          <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
            Example rates (UI estimation only): vCPU ${CPU_RATE}/h, Memory ${MEM_RATE}/GB·h, Storage
            ${STORAGE_RATE}/GB·h.
          </Typography>
        </Box>
      </Card>
    </Stack>
  );
}
