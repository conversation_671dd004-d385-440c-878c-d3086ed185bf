import { auth } from '@clerk/nextjs/server';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import Card from '@mui/joy/Card';
import Chip from '@mui/joy/Chip';
import Grid from '@mui/joy/Grid';
import Stack from '@mui/joy/Stack';
import Typography from '@mui/joy/Typography';
import { prisma } from '@sandock/domains/prisma';
import { getOrCreateUserSpace } from '@sandock/domains/sandbox/space';
import Link from 'next/link';
import { ensureUserByClerkId } from '../../lib/ensureUser';

// Rates duplicated from billing page to keep single-source (TODO: centralize)
const CPU_RATE = 0.05; // per vCPU hour USD (example)
const MEM_RATE = 0.01; // per GB hour USD
const STORAGE_RATE = 0.0005; // per GB hour USD (placeholder)

const SIGN_IN_PATH = '/sign-in' as const;

export default async function AppHome() {
  const { userId } = await auth();
  let sandboxCount: number | null = null;
  let runningCount = 0;
  let totalCPURunning = 0; // instantaneous allocated vCPU of RUNNING sandboxes
  let totalMemGBRunning = 0; // GB
  const totalStorageGBRunning = 0; // placeholder (no field yet)
  let currentEstimatedCost = 0; // reuse billing logic approximate
  if (userId) {
    const user = await ensureUserByClerkId(userId);
    if (user) {
      const space = await getOrCreateUserSpace(user.id);
      const sandboxes = await prisma.sandbox.findMany({
        where: { spaceId: space.id, deletedAt: null },
        select: {
          status: true,
          cpuLimit: true,
          memoryLimit: true,
          lastStartedAt: true,
          accumulatedRunMs: true,
        },
      });
      sandboxCount = sandboxes.length;
      const nowMs = Date.now();
      // Aggregate current instantaneous usage + cost
      sandboxes.forEach((s) => {
        if (s.status === 'RUNNING') {
          runningCount += 1;
          const cpu = s.cpuLimit || 0;
          const memGB = (s.memoryLimit || 0) / 1024;
          totalCPURunning += cpu;
          totalMemGBRunning += memGB;
          // storage placeholder stays 0 until storage metrics available
        }
        // Cost: emulate billing page calculation (runtime so far including active)
        const accumulated = s.accumulatedRunMs || 0;
        const activeExtra =
          s.status === 'RUNNING' && s.lastStartedAt ? nowMs - s.lastStartedAt.getTime() : 0;
        const runtimeMs = accumulated + activeExtra;
        const billableMs = Math.ceil(runtimeMs / 60_000) * 60_000;
        const billableHours = billableMs / 3_600_000;
        const cpu = s.cpuLimit || 0;
        const memGB = (s.memoryLimit || 0) / 1024;
        const storageGB = 0; // placeholder
        const ratePerHour = cpu * CPU_RATE + memGB * MEM_RATE + storageGB * STORAGE_RATE;
        currentEstimatedCost += billableHours * ratePerHour;
      });
    }
  }
  return (
    <Stack spacing={4}>
      <Stack spacing={1}>
        <Typography level="h2" sx={{ fontWeight: 600 }}>
          Sandock 概览
        </Typography>
      </Stack>

      {userId && (
        <Grid container spacing={2} columns={12} sx={{ '--Grid-minWidth': '220px' }}>
          <Grid xs={12} sm={4} md={4}>
            <Card variant="outlined" sx={{ p: 2 }}>
              <Typography level="title-sm">运行中 / 总 Sandbox</Typography>
              <Typography level="h4" sx={{ mt: 0.5 }}>
                {runningCount} / {sandboxCount ?? 0}
              </Typography>
              <Chip
                size="sm"
                variant="soft"
                color={runningCount > 0 ? 'success' : 'neutral'}
                sx={{ mt: 1 }}
              >
                {runningCount > 0 ? '活跃' : '无运行'}
              </Chip>
            </Card>
          </Grid>
          <Grid xs={12} sm={4} md={4}>
            <Card variant="outlined" sx={{ p: 2 }}>
              <Typography level="title-sm">即时资源使用</Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 0.5 }}>
                <Typography level="body-sm">vCPU: {totalCPURunning}</Typography>
                <Typography level="body-sm">内存: {totalMemGBRunning.toFixed(2)} GB</Typography>
                <Typography level="body-xs" sx={{ color: 'text.tertiary' }}>
                  Storage: {totalStorageGBRunning.toFixed(2)} GB
                </Typography>
              </Box>
            </Card>
          </Grid>
          <Grid xs={12} sm={4} md={4}>
            <Card variant="outlined" sx={{ p: 2 }}>
              <Typography level="title-sm">本周期估算费用</Typography>
              <Typography level="h4" sx={{ mt: 0.5 }}>
                ${currentEstimatedCost.toFixed(2)}
              </Typography>
              <Chip size="sm" variant="soft" color="primary" sx={{ mt: 1 }}>
                实时估算
              </Chip>
            </Card>
          </Grid>
        </Grid>
      )}

      <Grid container spacing={2} columns={12} sx={{ '--Grid-minWidth': '240px' }}>
        <Grid xs={12} sm={6} md={4}>
          <Card
            variant="outlined"
            sx={{ height: '100%', display: 'flex', flexDirection: 'column', gap: 1.5 }}
          >
            <Typography level="title-md">Sandboxes</Typography>
            <Typography level="body-sm" sx={{ flex: 1, color: 'text.secondary' }}>
              查看和管理你的沙盒环境。
            </Typography>
            <Link href="/dashboard/sandboxes" style={{ textDecoration: 'none' }}>
              <Button size="sm" variant="solid">
                进入
              </Button>
            </Link>
          </Card>
        </Grid>
        <Grid xs={12} sm={6} md={4}>
          <Card
            variant="outlined"
            sx={{ height: '100%', display: 'flex', flexDirection: 'column', gap: 1.5 }}
          >
            <Typography level="title-md">API Keys</Typography>
            <Typography level="body-sm" sx={{ flex: 1, color: 'text.secondary' }}>
              管理用于访问 Sandock API 的密钥（API Keys）。
            </Typography>
            <Link href="/dashboard/api-keys" style={{ textDecoration: 'none' }}>
              <Button size="sm" variant="soft">
                管理
              </Button>
            </Link>
          </Card>
        </Grid>
        <Grid xs={12} sm={6} md={4}>
          <Card
            variant="outlined"
            sx={{ height: '100%', display: 'flex', flexDirection: 'column', gap: 1.5 }}
          >
            <Typography level="title-md">Billing</Typography>
            <Typography level="body-sm" sx={{ flex: 1, color: 'text.secondary' }}>
              查看资源使用与费用估算。
            </Typography>
            <Link href="/dashboard/billing" style={{ textDecoration: 'none' }}>
              <Button size="sm" variant="soft" color="neutral">
                查看
              </Button>
            </Link>
          </Card>
        </Grid>
      </Grid>

      <Card variant="soft" sx={{ p: 3 }}>
        {userId ? (
          <Stack spacing={1}>
            <Typography level="body-md">
              Logged in as <b>{userId}</b>
            </Typography>
            <Chip size="sm" color="primary" variant="soft" sx={{ alignSelf: 'flex-start' }}>
              Sandboxes: {sandboxCount ?? 0}
            </Chip>
          </Stack>
        ) : (
          <Stack spacing={1}>
            <Typography level="body-md">登录以查看你的资源。</Typography>
            <Link href={{ pathname: SIGN_IN_PATH }} style={{ textDecoration: 'none' }}>
              <Button variant="solid" color="primary" size="sm">
                Sign in
              </Button>
            </Link>
          </Stack>
        )}
      </Card>
    </Stack>
  );
}
