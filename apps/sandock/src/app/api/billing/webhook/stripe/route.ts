import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import type Stripe from 'stripe';
import { downgradeSpaceToHobby, setProSubscriptionForSpace } from '../../lib/billing';
import { stripe } from '../../lib/stripe';
// Remove const assertion to satisfy Next.js config checker
export const config = { api: { bodyParser: false } };

async function readRawBody(req: NextRequest): Promise<string> {
  const arrayBuffer = await req.arrayBuffer();
  return Buffer.from(arrayBuffer).toString('utf8');
}

export async function POST(req: NextRequest) {
  const signature = req.headers.get('stripe-signature');
  if (!signature) return NextResponse.json({ error: 'missing-signature' }, { status: 400 });

  const rawBody = await readRawBody(req);
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || '';
  let event: Stripe.Event;
  try {
    event = stripe.webhooks.constructEvent(rawBody, signature, webhookSecret);
  } catch (err) {
    const message = err instanceof Error ? err.message : 'unknown-error';
    return NextResponse.json({ error: 'invalid-signature', message }, { status: 400 });
  }

  switch (event.type) {
    case 'checkout.session.completed': {
      const session = event.data.object as Stripe.Checkout.Session;
      const subscriptionId =
        typeof session.subscription === 'string' ? session.subscription : session.subscription?.id;
      if (subscriptionId) {
        const sub = await stripe.subscriptions.retrieve(subscriptionId);
        const customerId = typeof sub.customer === 'string' ? sub.customer : sub.customer?.id;
        // Resolve spaceId (preferred) or fallback internalUserId
        let spaceId: string | undefined;
        let internalUserId: string | undefined;
        if (customerId) {
          const customer = await stripe.customers.retrieve(customerId);
          // metadata is typed as Stripe.Metadata, index signature string -> string | null
          spaceId = (customer as Stripe.Customer).metadata?.spaceId || session.metadata?.spaceId;
          internalUserId =
            (customer as Stripe.Customer).metadata?.userId || session.metadata?.internalUserId;
        }
        if (spaceId) {
          await setProSubscriptionForSpace(spaceId, subscriptionId, sub.current_period_end);
        } else if (internalUserId) {
          // fallback legacy path
          await setProSubscriptionForSpace(
            await (async () => {
              // We only know user; need space helper lazily to avoid import cycle
              const { getOrCreateUserSpace } = await import('@sandock/domains/sandbox/space');
              return (await getOrCreateUserSpace(internalUserId)).id;
            })(),
            subscriptionId,
            sub.current_period_end,
          );
        }
      }
      break;
    }
    case 'customer.subscription.deleted': {
      const sub = event.data.object as Stripe.Subscription;
      const customerId = typeof sub.customer === 'string' ? sub.customer : sub.customer?.id;
      if (customerId) {
        const customer = await stripe.customers.retrieve(customerId);
        const spaceId = (customer as Stripe.Customer).metadata?.spaceId;
        const internalUserId = (customer as Stripe.Customer).metadata?.userId;
        if (spaceId) {
          await downgradeSpaceToHobby(spaceId);
        } else if (internalUserId) {
          const { getOrCreateUserSpace } = await import('@sandock/domains/sandbox/space');
          const space = await getOrCreateUserSpace(internalUserId);
          await downgradeSpaceToHobby(space.id);
        }
      }
      break;
    }
    default:
      break;
  }

  return NextResponse.json({ received: true });
}
