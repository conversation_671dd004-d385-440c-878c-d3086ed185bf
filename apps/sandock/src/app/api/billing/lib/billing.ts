import { getPlanDefinition } from '@sandock/domains/billing/plan-config';
import type { BillingPlanId } from '@sandock/domains/billing/types';
import { prisma } from '@sandock/domains/prisma/db';
import type Stripe from 'stripe';
import { stripe } from './stripe';

// Price cache (plan@version -> priceId)
const priceCache = new Map<string, string>();

// getPlanDefinition imported from shared plan-registry

export async function getOrCreatePlanPriceId(plan: BillingPlanId): Promise<string | null> {
  const def = getPlanDefinition(plan);
  if (!def) throw new Error(`Unknown plan ${plan}`);
  if (def.amountCents == null || def.amountCents <= 0) return null;
  if (!def.currency || !def.interval) throw new Error(`Plan ${plan} missing currency/interval`);
  const cacheKey = `${plan}@${def.version}`;
  const cached = priceCache.get(cacheKey);
  if (cached) return cached;

  let product: Stripe.Product | undefined;
  try {
    const products = await stripe.products.list({ active: true, limit: 100 });
    product =
      products.data.find((p) => p.metadata?.plan === plan) ||
      products.data.find((p) => p.name === def.productName);
  } catch {}

  if (product) {
    const needsUpdate =
      product.name !== def.productName ||
      product.description !== def.description ||
      product.metadata?.plan !== plan ||
      product.metadata?.version !== def.version;
    if (needsUpdate) {
      try {
        product = await stripe.products.update(product.id, {
          name: def.productName,
          description: def.description,
          metadata: { plan, role: 'plan', version: def.version },
          active: true,
        });
      } catch {}
    }
  } else {
    product = await stripe.products.create({
      name: def.productName,
      description: def.description,
      metadata: { plan, role: 'plan', version: def.version },
      active: true,
    });
  }

  let price: Stripe.Price | undefined;
  try {
    const prices = await stripe.prices.list({ product: product.id, active: true, limit: 100 });
    price =
      prices.data.find((p) => p.metadata?.plan === plan && p.metadata?.version === def.version) ||
      prices.data.find(
        (p) =>
          p.unit_amount === def.amountCents &&
          p.currency === def.currency &&
          p.recurring?.interval === def.interval,
      );
  } catch {}

  if (!price || price.metadata?.version !== def.version) {
    const currency = def.currency as string; // validated earlier
    const interval = def.interval as 'month' | 'year';
    price = await stripe.prices.create({
      product: product.id,
      currency,
      unit_amount: def.amountCents,
      recurring: { interval },
      nickname: `${def.productName} ${def.amountCents / 100}${interval === 'month' ? '/mo' : '/yr'}`,
      metadata: { plan, version: def.version },
    });
  }
  priceCache.set(cacheKey, price.id);
  return price.id;
}

export async function getOrCreateProPriceId(): Promise<string> {
  const id = await getOrCreatePlanPriceId('PRO');
  if (!id) throw new Error('Pro plan not billable');
  return id;
}

// Space-first helpers (preferred)
export async function ensureStripeCustomerForSpace(spaceId: string): Promise<string> {
  const billing = await prisma.spaceBilling.findUnique({ where: { spaceId } });
  if (billing?.stripeCustomerId) return billing.stripeCustomerId;

  // Attempt to find existing customer by metadata if previously created
  let existing: Stripe.Customer | undefined;
  try {
    const search = await stripe.customers.search({
      query: `metadata['spaceId']:'${spaceId}'`,
      limit: 1,
    });
    existing = search.data[0];
  } catch {}

  const customer =
    existing ||
    (await stripe.customers.create({
      metadata: { spaceId },
    }));

  await prisma.spaceBilling.upsert({
    where: { spaceId },
    update: { stripeCustomerId: customer.id },
    create: { spaceId, plan: 'HOBBY', stripeCustomerId: customer.id },
  });
  return customer.id;
}

export async function downgradeSpaceToHobby(spaceId: string) {
  const billing = await prisma.spaceBilling.findUnique({ where: { spaceId } });
  if (!billing) return;
  if (billing.stripeSubscriptionId) {
    try {
      await stripe.subscriptions.cancel(billing.stripeSubscriptionId, { prorate: true });
    } catch {}
  }
  await prisma.spaceBilling.update({
    where: { spaceId },
    data: { plan: 'HOBBY', stripeSubscriptionId: null, currentPeriodEnd: null },
  });
}

export async function setProSubscriptionForSpace(
  spaceId: string,
  subscriptionId: string,
  currentPeriodEnd: number | null | undefined,
) {
  await prisma.spaceBilling.upsert({
    where: { spaceId },
    update: {
      plan: 'PRO',
      stripeSubscriptionId: subscriptionId,
      currentPeriodEnd: currentPeriodEnd ? new Date(currentPeriodEnd * 1000) : null,
    },
    create: {
      spaceId,
      plan: 'PRO',
      stripeSubscriptionId: subscriptionId,
      currentPeriodEnd: currentPeriodEnd ? new Date(currentPeriodEnd * 1000) : null,
    },
  });
}

export async function getCurrentSpacePlanBySpaceId(spaceId: string): Promise<BillingPlanId> {
  const billing = await prisma.spaceBilling.findUnique({ where: { spaceId } });
  return (billing?.plan as BillingPlanId) || 'HOBBY';
}

// Backward-compatible userId wrappers (to be removed later)
