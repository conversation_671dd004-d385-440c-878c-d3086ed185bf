import { prisma } from '@sandock/domains/prisma/db';
import { beforeAll, describe, expect, it, vi } from 'vitest';
import {
  downgradeSpaceToHobby,
  ensureStripeCustomerForSpace,
  getCurrentSpacePlanBySpaceId,
  setProSubscriptionForSpace,
} from '../billing';
import { stripe } from '../stripe';

// Stripe calls are networked; we mock minimal functions to isolate DB logic.
vi.mock('../stripe', () => ({
  stripe: {
    customers: {
      search: vi.fn().mockResolvedValue({ data: [] }),
      create: vi.fn().mockResolvedValue({ id: 'cus_mock', metadata: { spaceId: 'space_test' } }),
      retrieve: vi.fn(),
      update: vi.fn(),
    },
    products: { list: vi.fn(), create: vi.fn(), update: vi.fn() },
    prices: { list: vi.fn(), create: vi.fn() },
    subscriptions: { cancel: vi.fn(), retrieve: vi.fn() },
  },
}));

async function createUserAndSpace() {
  const user = await prisma.user.create({
    data: { id: 'user_test', email: '<EMAIL>', clerkId: 'clerk_user_test' },
  });
  const space = await prisma.space.create({ data: { id: 'space_test', ownerId: user.id } });
  return { user, space };
}

describe('Space billing helpers', () => {
  beforeAll(async () => {
    // Clean existing test records (idempotent)
    await prisma.spaceBilling.deleteMany({ where: { spaceId: 'space_test' } });
    await prisma.space.deleteMany({ where: { id: 'space_test' } });
    await prisma.user.deleteMany({ where: { id: 'user_test' } });
    await createUserAndSpace();
  });

  it('ensures stripe customer (idempotent) and defaults to HOBBY', async () => {
    const first = await ensureStripeCustomerForSpace('space_test');
    const second = await ensureStripeCustomerForSpace('space_test');
    expect(first).toBe('cus_mock');
    expect(second).toBe('cus_mock');
    // create should have run only once
    const createMock = stripe.customers.create as unknown as { mock: { calls: unknown[] } };
    expect(createMock.mock.calls.length).toBe(1);
    const plan = await getCurrentSpacePlanBySpaceId('space_test');
    expect(plan).toBe('HOBBY');
  });

  it('sets PRO subscription for a space and stores currentPeriodEnd', async () => {
    const ts = 1_700_000_000; // seconds
    await setProSubscriptionForSpace('space_test', 'sub_123', ts);
    const billing = await prisma.spaceBilling.findUnique({ where: { spaceId: 'space_test' } });
    expect(billing?.plan).toBe('PRO');
    expect(billing?.stripeSubscriptionId).toBe('sub_123');
    expect(billing?.currentPeriodEnd?.getTime()).toBe(ts * 1000);
  });

  it('updates existing PRO subscription (idempotent upsert) with new subscriptionId and period end', async () => {
    const ts2 = 1_710_000_000; // seconds
    await setProSubscriptionForSpace('space_test', 'sub_456', ts2);
    const billing = await prisma.spaceBilling.findUnique({ where: { spaceId: 'space_test' } });
    expect(billing?.plan).toBe('PRO');
    expect(billing?.stripeSubscriptionId).toBe('sub_456');
    expect(billing?.currentPeriodEnd?.getTime()).toBe(ts2 * 1000);
    // ensure only a single billing row exists
    const count = await prisma.spaceBilling.count({ where: { spaceId: 'space_test' } });
    expect(count).toBe(1);
  });

  it('sets PRO subscription with null currentPeriodEnd', async () => {
    await setProSubscriptionForSpace('space_test', 'sub_null', null);
    const billing = await prisma.spaceBilling.findUnique({ where: { spaceId: 'space_test' } });
    expect(billing?.plan).toBe('PRO');
    expect(billing?.stripeSubscriptionId).toBe('sub_null');
    expect(billing?.currentPeriodEnd).toBeNull();
  });

  it('downgrades space to HOBBY and clears subscription/currentPeriodEnd with single cancel call', async () => {
    const beforeCancelCalls = (
      stripe.subscriptions.cancel as unknown as { mock: { calls: unknown[] } }
    ).mock.calls.length;
    await downgradeSpaceToHobby('space_test');
    const billing = await prisma.spaceBilling.findUnique({ where: { spaceId: 'space_test' } });
    expect(billing?.plan).toBe('HOBBY');
    expect(billing?.stripeSubscriptionId).toBeNull();
    expect(billing?.currentPeriodEnd).toBeNull();
    const afterFirst = (stripe.subscriptions.cancel as unknown as { mock: { calls: unknown[] } })
      .mock.calls.length;
    expect(afterFirst).toBe(beforeCancelCalls + 1);
    // second downgrade should NOT call cancel again (already no subscription)
    await downgradeSpaceToHobby('space_test');
    const afterSecond = (stripe.subscriptions.cancel as unknown as { mock: { calls: unknown[] } })
      .mock.calls.length;
    expect(afterSecond).toBe(afterFirst);
  });
});
