import { getOrCreateUserSpace } from '@sandock/domains/sandbox/space';
import { NextResponse } from 'next/server';
import { ensureUserByClerkId } from '../../../../../lib/ensureUser';
import { safeAuth } from '../../../../../lib/safeAuth';
import { setProSubscriptionForSpace } from '../../lib/billing';
import { stripe } from '../../lib/stripe';

// Optional verification endpoint: client can call after redirect success to force a plan refresh
export async function POST(req: Request) {
  const { userId: clerkUserId } = await safeAuth(req);
  if (!clerkUserId) return NextResponse.json({ error: 'unauthorized' }, { status: 401 });
  const { session_id } = await req.json().catch(() => ({ session_id: undefined }));
  if (!session_id) return NextResponse.json({ error: 'missing-session-id' }, { status: 400 });
  try {
    const session = await stripe.checkout.sessions.retrieve(session_id);
    if (session.payment_status !== 'paid') {
      return NextResponse.json({ status: 'pending' });
    }
    const subscriptionId =
      typeof session.subscription === 'string' ? session.subscription : session.subscription?.id;
    if (!subscriptionId) return NextResponse.json({ error: 'no-subscription' }, { status: 400 });
    const internalUser = await ensureUserByClerkId(clerkUserId);
    if (!internalUser) return NextResponse.json({ error: 'user-sync-failed' }, { status: 500 });
    const sub = await stripe.subscriptions.retrieve(subscriptionId);
    const space = await getOrCreateUserSpace(internalUser.id);
    await setProSubscriptionForSpace(space.id, subscriptionId, sub.current_period_end);
    return NextResponse.json({ status: 'ok', plan: 'PRO' });
  } catch (e) {
    return NextResponse.json(
      { error: 'verify-failed', message: e instanceof Error ? e.message : 'unknown' },
      { status: 500 },
    );
  }
}
