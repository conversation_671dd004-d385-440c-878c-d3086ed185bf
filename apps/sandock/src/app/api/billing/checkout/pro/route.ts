import { getOrCreateUserSpace } from '@sandock/domains/sandbox/space';
import { NextResponse } from 'next/server';
import { ensureUserByClerkId } from '../../../../../lib/ensureUser';
import { safeAuth } from '../../../../../lib/safeAuth';
import { ensureStripeCustomerForSpace, getOrCreateProPriceId } from '../../lib/billing';
import { stripe } from '../../lib/stripe';

export async function POST(req: Request) {
  const { userId } = await safeAuth(req);
  if (!userId)
    return NextResponse.json({ error: 'unauthorized: no auth context' }, { status: 401 });

  // Map clerk user to internal user id (create user if not exists)
  const internalUser = await ensureUserByClerkId(userId);
  if (!internalUser) {
    return NextResponse.json({ error: 'user-sync-failed' }, { status: 500 });
  }
  const internalUserId = internalUser.id;
  const space = await getOrCreateUserSpace(internalUserId);
  const customerId = await ensureStripeCustomerForSpace(space.id);
  // lazy 不重复，自动创建 product 和 price
  const priceId = await getOrCreateProPriceId();
  const baseUrl = process.env.APP_BASE_URL || 'http://localhost:3030';
  const session = await stripe.checkout.sessions.create({
    mode: 'subscription',
    customer: customerId,
    line_items: [{ price: priceId, quantity: 1 }],
    success_url: `${baseUrl}/dashboard/billing?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${baseUrl}/dashboard/billing?canceled=1`,
    metadata: { internalUserId, clerkUserId: userId, spaceId: space.id, plan: 'PRO' },
  });
  return NextResponse.json({ url: session.url });
}
