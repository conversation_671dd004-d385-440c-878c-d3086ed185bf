import { getOrCreateUserSpace } from '@sandock/domains/sandbox/space';
import { NextResponse } from 'next/server';
import { ensureUserByClerkId } from '../../../../lib/ensureUser';
import { safeAuth } from '../../../../lib/safeAuth';
import { ensureStripeCustomerForSpace } from '../lib/billing';
import { stripe } from '../lib/stripe';

let cachedPortalConfigId: string | undefined;

async function ensurePortalConfiguration(): Promise<string | undefined> {
  if (cachedPortalConfigId) return cachedPortalConfigId;
  try {
    const list = await stripe.billingPortal.configurations.list({ limit: 10 });
    const active = list.data.find((c) => c.active);
    if (active) {
      cachedPortalConfigId = active.id;
      return cachedPortalConfigId;
    }
    const created = await stripe.billingPortal.configurations.create({
      business_profile: { headline: 'Manage your Sandock subscription' },
      features: {
        customer_update: { enabled: true, allowed_updates: ['email'] },
        invoice_history: { enabled: true },
        payment_method_update: { enabled: true },
        subscription_cancel: { enabled: true, mode: 'at_period_end' },
      },
    });
    cachedPortalConfigId = created.id;
    return cachedPortalConfigId;
  } catch {
    return undefined; // fallback to default attempt
  }
}

export async function POST(req: Request) {
  const { userId } = await safeAuth(req);
  if (!userId)
    return NextResponse.json({ error: 'unauthorized: no auth context' }, { status: 401 });
  const internalUser = await ensureUserByClerkId(userId);
  if (!internalUser) {
    return NextResponse.json({ error: 'user-sync-failed' }, { status: 500 });
  }
  const internalUserId = internalUser.id;
  const space = await getOrCreateUserSpace(internalUserId);
  const customerId = await ensureStripeCustomerForSpace(space.id);
  const baseUrl = process.env.APP_BASE_URL || 'http://localhost:3030';
  // First attempt without forcing configuration (lazy path)
  const first = await stripe.billingPortal.sessions
    .create({ customer: customerId, return_url: `${baseUrl}/dashboard/billing` })
    .catch((err) => err as Error);

  if (first instanceof Error) {
    const msg = first.message || '';
    if (!msg.includes('No configuration provided')) {
      return NextResponse.json({ error: 'portal-session-failed', message: msg }, { status: 500 });
    }

    // 仅当错误包含 “No configuration provided” 时才
    // ensurePortalConfiguration 并二次重试。
    // Lazy ensure only when needed
    const configId = await ensurePortalConfiguration();
    if (!configId) {
      return NextResponse.json(
        {
          error: 'portal-no-config',
          message:
            'Stripe portal configuration missing. Set STRIPE_PORTAL_CONFIGURATION_ID or create a default in dashboard.',
        },
        { status: 500 },
      );
    }
    const second = await stripe.billingPortal.sessions
      .create({
        customer: customerId,
        return_url: `${baseUrl}/dashboard/billing`,
        configuration: configId,
      })
      .catch((err) => err as Error);
    if (second instanceof Error) {
      return NextResponse.json(
        {
          error: 'portal-session-failed-after-config',
          message: second.message,
          configurationIdTried: configId,
        },
        { status: 500 },
      );
    }
    return NextResponse.json({ url: second.url });
  }
  return NextResponse.json({ url: first.url });
}
