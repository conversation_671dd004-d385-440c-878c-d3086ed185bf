import { createRoute, type <PERSON><PERSON><PERSON><PERSON><PERSON>, z } from '@hono/zod-openapi';
import type { SandboxCodeOptions, SandboxShellOptions } from '@sandock/domains/sandbox/types';
import { createResponseVOSchema, ResponseVOBuilder } from 'sharelib/openapi/response-vo';
import sandboxService from './sandbox-service';

// Shared execution result schema
const ExecutionResultSchema = z
  .object({
    stdout: z.string(),
    stderr: z.string(),
    exitCode: z.number().nullable(),
    timedOut: z.boolean(),
    durationMs: z.number(),
  })
  .openapi('SandboxExecutionResult');

export function registerSandboxRoutes(app: OpenAPIHono) {
  // Create sandbox
  const CreateSandboxRoute = createRoute({
    method: 'post',
    path: '/sandbox',
    tags: ['sandbox'],
    request: {
      body: {
        content: {
          'application/json': {
            schema: z
              .object({
                spaceId: z.string().min(1),
                actorUserId: z.string().min(1),
                // Provider removed: server chooses via SANDBOX_PROVIDER env.
                image: z.string().min(1).optional().openapi({ example: 'node:20-bullseye' }),
                pythonImage: z.string().min(1).optional().openapi({ example: 'python:3.12-slim' }),
                pull: z.boolean().optional(),
                memoryLimitMb: z.number().int().positive().optional(),
                cpuShares: z.number().int().positive().optional(),
                workdir: z.string().optional(), // local/docker
                keep: z.boolean().optional(), // local
              })
              .partial()
              .openapi('CreateSandboxRequest'),
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Created sandbox',
        content: {
          'application/json': {
            schema: createResponseVOSchema(z.object({ id: z.string() })),
          },
        },
      },
    },
  });
  app.openapi(CreateSandboxRoute, async (c) => {
    const body = c.req.valid('json') as {
      spaceId: string;
      actorUserId: string;
      title?: string;
      image?: string;
      cpuShares?: number;
      memoryLimitMb?: number;
      [k: string]: unknown;
    };
    const { spaceId, actorUserId, title, image, cpuShares, memoryLimitMb, ...rest } = body;
    const res = await sandboxService.createSandbox({
      spaceId,
      actorUserId,
      title,
      image,
      cpuLimit: cpuShares,
      memoryLimit: memoryLimitMb,
      ...rest,
    });
    return c.json(ResponseVOBuilder.success(res), 200);
  });

  const StartSandboxRoute = createRoute({
    method: 'post',
    path: '/sandbox/{id}/start',
    tags: ['sandbox'],
    request: {
      params: z.object({ id: z.string().min(1).openapi({ example: 'user:local:abcd1234' }) }),
    },
    responses: {
      200: {
        description: 'Started (or reused) sandbox',
        content: {
          'application/json': {
            schema: createResponseVOSchema(z.object({ id: z.string(), started: z.boolean() })),
          },
        },
      },
    },
  });
  app.openapi(StartSandboxRoute, async (c) => {
    const { id } = c.req.valid('param');
    const res = await sandboxService.startSandbox(id);
    return c.json(ResponseVOBuilder.success(res), 200);
  });

  // Run code
  const RunCodeRoute = createRoute({
    method: 'post',
    path: '/sandbox/{id}/code',
    tags: ['sandbox'],
    request: {
      params: z.object({ id: z.string().min(1) }),
      body: {
        content: {
          'application/json': {
            schema: z
              .object({
                language: z.enum(['javascript', 'typescript', 'python']),
                code: z.string(),
                timeoutMs: z.number().int().positive().optional(),
                input: z.string().optional(),
              })
              .openapi('RunCodeRequest'),
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Code execution result',
        content: {
          'application/json': { schema: createResponseVOSchema(ExecutionResultSchema) },
        },
      },
    },
  });
  app.openapi(RunCodeRoute, async (c) => {
    const { id } = c.req.valid('param');
    const body = c.req.valid('json');
    const result = await sandboxService.runCode(id, body as SandboxCodeOptions);
    return c.json(ResponseVOBuilder.success(result), 200);
  });

  // Shell
  const ShellRoute = createRoute({
    method: 'post',
    path: '/sandbox/{id}/shell',
    tags: ['sandbox'],
    request: {
      params: z.object({ id: z.string().min(1) }),
      body: {
        content: {
          'application/json': {
            schema: z
              .object({
                cmd: z.union([z.string(), z.array(z.string())]),
                timeoutMs: z.number().int().positive().optional(),
                workdir: z.string().optional(),
                env: z.record(z.string()).optional(),
                input: z.string().optional(),
              })
              .openapi('ShellRequest'),
          },
        },
      },
    },
    responses: {
      200: {
        description: 'Shell command result',
        content: {
          'application/json': { schema: createResponseVOSchema(ExecutionResultSchema) },
        },
      },
    },
  });
  app.openapi(ShellRoute, async (c) => {
    const { id } = c.req.valid('param');
    const body = c.req.valid('json');
    const result = await sandboxService.shellExec(id, body as SandboxShellOptions);
    return c.json(ResponseVOBuilder.success(result), 200);
  });

  // Write file
  const WriteFileRoute = createRoute({
    method: 'post',
    path: '/sandbox/{id}/fs/write',
    tags: ['sandbox'],
    request: {
      params: z.object({ id: z.string().min(1) }),
      body: {
        content: {
          'application/json': {
            schema: z
              .object({
                path: z.string().min(1).openapi({ example: 'src/index.js' }),
                content: z.string(),
                executable: z.boolean().optional(),
              })
              .openapi('WriteFileRequest'),
          },
        },
      },
    },
    responses: {
      200: {
        description: 'File written',
        content: { 'application/json': { schema: createResponseVOSchema(z.boolean()) } },
      },
    },
  });
  app.openapi(WriteFileRoute, async (c) => {
    const { id } = c.req.valid('param');
    const body = c.req.valid('json') as { path: string; content: string; executable?: boolean };
    const ok = await sandboxService.writeFile(id, body);
    return c.json(ResponseVOBuilder.success(ok), 200);
  });

  // Read file
  const ReadFileRoute = createRoute({
    method: 'get',
    path: '/sandbox/{id}/fs/read',
    tags: ['sandbox'],
    request: {
      params: z.object({ id: z.string().min(1) }),
      query: z.object({ path: z.string().min(1) }),
    },
    responses: {
      200: {
        description: 'File content',
        content: {
          'application/json': {
            schema: createResponseVOSchema(z.object({ path: z.string(), content: z.string() })),
          },
        },
      },
    },
  });
  app.openapi(ReadFileRoute, async (c) => {
    const { id } = c.req.valid('param');
    const { path } = c.req.valid('query');
    const res = await sandboxService.readFile(id, path);
    return c.json(ResponseVOBuilder.success(res), 200);
  });

  // List files
  const ListFilesRoute = createRoute({
    method: 'get',
    path: '/sandbox/{id}/fs/list',
    tags: ['sandbox'],
    request: {
      params: z.object({ id: z.string().min(1) }),
      query: z.object({ path: z.string().optional() }),
    },
    responses: {
      200: {
        description: 'Directory entries',
        content: {
          'application/json': {
            schema: createResponseVOSchema(
              z.object({ path: z.string().default('.'), entries: z.array(z.string()) }),
            ),
          },
        },
      },
    },
  });
  app.openapi(ListFilesRoute, async (c) => {
    const { id } = c.req.valid('param');
    const { path } = c.req.valid('query');
    const res = await sandboxService.listFiles(id, path);
    return c.json(ResponseVOBuilder.success(res), 200);
  });

  // Remove file/dir
  const RemoveFileRoute = createRoute({
    method: 'delete',
    path: '/sandbox/{id}/fs',
    tags: ['sandbox'],
    request: {
      params: z.object({ id: z.string().min(1) }),
      query: z.object({ path: z.string().min(1) }),
    },
    responses: {
      200: {
        description: 'Removed file or directory',
        content: { 'application/json': { schema: createResponseVOSchema(z.boolean()) } },
      },
    },
  });
  app.openapi(RemoveFileRoute, async (c) => {
    const { id } = c.req.valid('param');
    const { path } = c.req.valid('query');
    const ok = await sandboxService.removePath(id, path);
    return c.json(ResponseVOBuilder.success(ok), 200);
  });

  // Stop sandbox
  const StopSandboxRoute = createRoute({
    method: 'post',
    path: '/sandbox/{id}/stop',
    tags: ['sandbox'],
    request: { params: z.object({ id: z.string().min(1) }) },
    responses: {
      200: {
        description: 'Stopped sandbox',
        content: {
          'application/json': {
            schema: createResponseVOSchema(z.object({ id: z.string(), stopped: z.boolean() })),
          },
        },
      },
    },
  });
  app.openapi(StopSandboxRoute, async (c) => {
    const { id } = c.req.valid('param');
    const res = await sandboxService.stopSandbox(id);
    return c.json(ResponseVOBuilder.success(res), 200);
  });

  // List sandboxes
  const ListSandboxesRoute = createRoute({
    method: 'get',
    path: '/sandbox',
    tags: ['sandbox'],
    request: {
      query: z.object({
        spaceId: z.string().min(1),
      }),
    },
    responses: {
      200: {
        description: 'Managed sandboxes',
        content: {
          'application/json': {
            schema: createResponseVOSchema(
              z
                .object({
                  items: z.array(z.object({ id: z.string() })),
                })
                .openapi('SandboxListResponse'),
            ),
          },
        },
      },
    },
  });
  app.openapi(ListSandboxesRoute, (c) => {
    const { spaceId } = c.req.valid('query');
    return c.json(ResponseVOBuilder.success(sandboxService.listSandboxes(spaceId)), 200);
  });
}

export default registerSandboxRoutes;
