import type { SandboxProvider as DbSandboxProvider } from '@sandock/domains/prisma';
import { persistentSandboxManager } from '@sandock/domains/sandbox/sandbox-manager';
import type {
  SandboxCodeOptions,
  SandboxProvider,
  SandboxShellOptions,
} from '@sandock/domains/sandbox/types';

// Create new sandbox (client can pass nothing but server chooses provider)
export async function createSandbox(options: {
  spaceId: string;
  actorUserId: string;
  title?: string;
  image?: string;
  cpuLimit?: number;
  memoryLimit?: number;
  [k: string]: unknown;
}) {
  const {
    spaceId,
    actorUserId,
    title = 'Untitled Sandbox',
    image,
    cpuLimit,
    memoryLimit,
    ...metadata
  } = options;
  const sb = await persistentSandboxManager.create({
    spaceId,
    actorUserId,
    title,
    // use default
    forceProvider: undefined,
    image,
    cpuLimit,
    memoryLimit,
    metadata,
  });
  return { id: sb.id };
}

async function getSandbox(id: string): Promise<SandboxProvider> {
  return persistentSandboxManager.getProvider(id);
}

export async function startSandbox(id: string) {
  // get will start if needed
  await getSandbox(id);
  return { id, started: true };
}

export async function runCode(id: string, opts: SandboxCodeOptions) {
  const sbx = await getSandbox(id);
  return sbx.runCode(opts);
}

export async function shellExec(id: string, opts: SandboxShellOptions) {
  const sbx = await getSandbox(id);
  return sbx.shell(opts);
}

export async function writeFile(
  id: string,
  file: { path: string; content: string; executable?: boolean },
) {
  const sbx = await getSandbox(id);
  await sbx.writeFile(file);
  return true;
}

export async function readFile(id: string, path: string) {
  const sbx = await getSandbox(id);
  const content = await sbx.readFile(path);
  return { path, content };
}

export async function listFiles(id: string, path?: string) {
  const sbx = await getSandbox(id);
  const entries = await sbx.list(path);
  return { path: path ?? '.', entries };
}

export async function removePath(id: string, path: string) {
  const sbx = await getSandbox(id);
  await sbx.remove(path);
  return true;
}

export async function stopSandbox(id: string) {
  const sbx = await getSandbox(id);
  await sbx.stop();
  return { id, stopped: true };
}

export async function listSandboxes(spaceId: string) {
  const items = await persistentSandboxManager.list({ spaceId });
  return { items: items.map((s) => ({ id: s.id, status: s.status })) };
}

export const sandboxService = {
  createSandbox,
  startSandbox,
  runCode,
  shellExec,
  writeFile,
  readFile,
  listFiles,
  removePath,
  stopSandbox,
  listSandboxes,
};

export default sandboxService;
