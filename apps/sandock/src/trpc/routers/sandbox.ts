import { persistentSandboxManager } from '@sandock/domains/sandbox/sandbox-manager';
import { getOrCreateUserSpace } from '@sandock/domains/sandbox/space';
import { TRPCError } from '@trpc/server';
import { z } from 'zod';
import { protectedProcedure, publicProcedure, router } from '../core';

const PAGE_SIZE = 10;
const USER_RUNNING_QUOTA = Math.max(
  1,
  Number.isNaN(Number(process.env.SANDBOX_USER_RUNNING_QUOTA))
    ? 5
    : Number(process.env.SANDBOX_USER_RUNNING_QUOTA),
); // per-user running sandbox limit (env override)

const listInput = z.object({
  page: z.number().int().min(1).default(1),
  q: z.string().trim().optional(),
});
const idInput = z.object({ id: z.string() });
const createSandboxInput = z.object({
  title: z.string().min(1).max(120),
  image: z.string().optional(),
  cpuLimit: z.number().int().positive().optional(),
  memoryLimit: z.number().int().positive().optional(),
  workdir: z.string().optional(),
});

async function reconcileSandboxStatuses() {
  // With persistent manager every operation ensures provider start; simple placeholder now.
  return;
}

export const sandboxRouter = router({
  stats: protectedProcedure.query(async ({ ctx }) => {
    if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
    const now = new Date();
    const since7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    // Parallel queries
    const space = await getOrCreateUserSpace(ctx.user.id);
    const [sandboxes, startEventsRecent, segments7d] = await Promise.all([
      ctx.prisma.sandbox.findMany({
        where: { spaceId: space.id, deletedAt: null },
        select: { id: true, status: true, accumulatedRunMs: true, lastStartedAt: true },
      }),
      ctx.prisma.sandboxLifecycleEvent.findMany({
        where: { sandbox: { spaceId: space.id }, type: 'START_SUCCESS' },
        orderBy: { ts: 'desc' },
        take: 200,
        select: { meta: true, ts: true },
      }),
      ctx.prisma.sandboxRunSegment.findMany({
        where: { sandbox: { spaceId: space.id }, startAt: { gte: since7d } },
        select: { startAt: true, endAt: true, durationMs: true },
      }),
    ]);
    const runningCount = sandboxes.filter((s) => s.status === 'RUNNING').length;
    const totalCount = sandboxes.length;
    const nowMs = now.getTime();
    let accumulated = 0;
    let activeExtra = 0;
    for (const s of sandboxes) {
      accumulated += s.accumulatedRunMs || 0;
      if (s.status === 'RUNNING' && s.lastStartedAt) {
        activeExtra += nowMs - s.lastStartedAt.getTime();
      }
    }
    const totalRunMs = accumulated + activeExtra;
    // Startup metrics
    const startupSamples: number[] = startEventsRecent
      .map((e) => {
        if (typeof e.meta === 'object' && e.meta && 'startupMs' in e.meta) {
          const val = (e.meta as Record<string, unknown>).startupMs;
          return typeof val === 'number' ? val : undefined;
        }
        return undefined;
      })
      .filter((v): v is number => typeof v === 'number');
    const lastStartupMs = startupSamples[0] ?? 0;
    const percentile = (arr: number[], p: number) => {
      if (!arr.length) return 0;
      const sorted = [...arr].sort((a, b) => a - b);
      const idx = (p / 100) * (sorted.length - 1);
      const lo = Math.floor(idx);
      const hi = Math.ceil(idx);
      if (lo === hi) return sorted[lo];
      const frac = idx - lo;
      return sorted[lo] + (sorted[hi] - sorted[lo]) * frac;
    };
    const avgStartupMs = startupSamples.length
      ? startupSamples.reduce((a, b) => a + b, 0) / startupSamples.length
      : 0;
    const p50StartupMs = percentile(startupSamples, 50);
    const p95StartupMs = percentile(startupSamples, 95);
    // Daily aggregation (runMs & billableRunMs) from segments, splitting across day boundaries
    interface DayAgg {
      runMs: number;
    }
    const dailyMap: Record<string, DayAgg> = {};
    for (const seg of segments7d) {
      const start = seg.startAt < since7d ? since7d : seg.startAt;
      const end = seg.endAt ?? now;
      if (end <= since7d) continue;
      let cursor = start;
      while (cursor < end) {
        const dayKey = cursor.toISOString().slice(0, 10); // YYYY-MM-DD
        const nextDay = new Date(cursor);
        nextDay.setUTCHours(24, 0, 0, 0);
        const sliceEnd = end < nextDay ? end : nextDay;
        const sliceMs = sliceEnd.getTime() - cursor.getTime();
        if (!dailyMap[dayKey]) dailyMap[dayKey] = { runMs: 0 };
        dailyMap[dayKey].runMs += sliceMs;
        cursor = sliceEnd;
      }
    }
    const daily = Object.entries(dailyMap)
      .map(([k, v]) => ({ day: k, runMs: v.runMs, billableMs: Math.ceil(v.runMs / 60000) * 60000 }))
      .sort((a, b) => (a.day < b.day ? -1 : 1));
    // Overall billable (recompute by summing per segment rounding + active)
    const billableClosed = segments7d
      .map((s) => s.durationMs || (s.endAt ?? now).getTime() - s.startAt.getTime())
      .map((d) => Math.ceil(d / 60000) * 60000)
      .reduce((a, b) => a + b, 0);
    const billableActive = Math.ceil(activeExtra / 60000) * 60000;
    const billableRunMs = billableClosed + billableActive;
    return {
      runningCount,
      totalCount,
      totalRunMs,
      avgStartupMs,
      lastStartupMs,
      p50StartupMs,
      p95StartupMs,
      billableRunMs,
      daily,
    };
  }),
  count: protectedProcedure.query(async ({ ctx }) => {
    if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
    const space = await getOrCreateUserSpace(ctx.user.id);
    const sandboxCount = await ctx.prisma.sandbox.count({
      where: { spaceId: space.id, deletedAt: null },
    });
    return { sandboxCount };
  }),
  create: protectedProcedure.input(createSandboxInput).mutation(async ({ ctx, input }) => {
    if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
    const space = await getOrCreateUserSpace(ctx.user.id);
    const runningCount = await ctx.prisma.sandbox.count({
      where: { spaceId: space.id, status: 'RUNNING', deletedAt: null },
    });
    if (runningCount >= USER_RUNNING_QUOTA)
      throw new TRPCError({ code: 'FORBIDDEN', message: 'Sandbox quota exceeded' });
    // Use persistent manager create (handles DB insert + provider start)
    try {
      const created = await persistentSandboxManager.create({
        spaceId: space.id,
        actorUserId: ctx.user.id,
        title: input.title,
        forceProvider: undefined,
        image: input.image,
        cpuLimit: input.cpuLimit,
        memoryLimit: input.memoryLimit,
        metadata: { workdir: input.workdir },
      });
      return created;
    } catch (e) {
      console.error(e);
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Sandbox create failed' });
    }
  }),
  start: protectedProcedure.input(idInput).mutation(async ({ ctx, input }) => {
    if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
    const space = await getOrCreateUserSpace(ctx.user.id);
    const sb = await ctx.prisma.sandbox.findFirst({ where: { id: input.id, spaceId: space.id } });
    if (!sb) throw new TRPCError({ code: 'NOT_FOUND', message: 'Sandbox not found' });
    if (sb.status === 'DELETING' || sb.status === 'DELETED')
      throw new TRPCError({ code: 'BAD_REQUEST', message: 'Cannot start deleted sandbox' });
    if (sb.status !== 'RUNNING') {
      const runningCount = await ctx.prisma.sandbox.count({
        where: { spaceId: space.id, status: 'RUNNING', deletedAt: null },
      });
      if (runningCount >= USER_RUNNING_QUOTA)
        throw new TRPCError({ code: 'FORBIDDEN', message: 'Sandbox quota exceeded' });
    }
    try {
      await persistentSandboxManager.getProvider(sb.id); // ensures provider running
      return ctx.prisma.sandbox.update({ where: { id: sb.id }, data: { status: 'RUNNING' } });
    } catch {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Sandbox start failed',
      });
    }
  }),
  stop: protectedProcedure.input(idInput).mutation(async ({ ctx, input }) => {
    if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
    const space = await getOrCreateUserSpace(ctx.user.id);
    const sb = await ctx.prisma.sandbox.findFirst({ where: { id: input.id, spaceId: space.id } });
    if (!sb) throw new TRPCError({ code: 'NOT_FOUND', message: 'Sandbox not found' });
    if (sb.status !== 'RUNNING')
      throw new TRPCError({ code: 'BAD_REQUEST', message: 'Sandbox not running' });
    try {
      try {
        await persistentSandboxManager.stop(sb.id);
      } catch {}
      return ctx.prisma.sandbox.update({ where: { id: sb.id }, data: { status: 'STOPPED' } });
    } catch {
      throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Sandbox stop failed' });
    }
  }),
  delete: protectedProcedure.input(idInput).mutation(async ({ ctx, input }) => {
    if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
    const space = await getOrCreateUserSpace(ctx.user.id);
    const sb = await ctx.prisma.sandbox.findFirst({ where: { id: input.id, spaceId: space.id } });
    if (!sb) throw new TRPCError({ code: 'NOT_FOUND', message: 'Sandbox not found' });
    if (sb.status === 'DELETED') return sb;
    // attempt to stop underlying provider
    try {
      await persistentSandboxManager.delete(sb.id);
    } catch {}
    return ctx.prisma.sandbox.update({
      where: { id: sb.id },
      data: { status: 'DELETED', deletedAt: new Date() },
    });
  }),
  refresh: protectedProcedure.input(idInput).query(async ({ ctx, input }) => {
    if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
    await reconcileSandboxStatuses();
    const space = await getOrCreateUserSpace(ctx.user.id);
    const sb = await ctx.prisma.sandbox.findFirst({ where: { id: input.id, spaceId: space.id } });
    if (!sb) throw new TRPCError({ code: 'NOT_FOUND' });
    return sb;
  }),
  // list with auto reconciliation
  list: protectedProcedure.input(listInput).query(async ({ ctx, input }) => {
    if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
    await reconcileSandboxStatuses();
    const { page, q } = input;
    const space = await getOrCreateUserSpace(ctx.user.id);
    const where: {
      spaceId: string;
      deletedAt: null;
      title?: { contains: string; mode: 'insensitive' };
    } = { spaceId: space.id, deletedAt: null };
    if (q) where.title = { contains: q, mode: 'insensitive' };
    const [total, items] = await Promise.all([
      ctx.prisma.sandbox.count({ where }),
      ctx.prisma.sandbox.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * PAGE_SIZE,
        take: PAGE_SIZE,
      }),
    ]);
    const totalPages = Math.max(1, Math.ceil(total / PAGE_SIZE));
    return { items, total, page, pageSize: PAGE_SIZE, totalPages };
  }),
  echo: publicProcedure
    .input(z.object({ text: z.string() }))
    .query(async ({ input }) => ({ text: input.text })),
});
