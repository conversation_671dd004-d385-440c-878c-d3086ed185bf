import crypto from 'node:crypto';
import { getOrCreateUserSpace } from '@sandock/domains/sandbox/space';
import { generateNanoID } from 'basenext/utils';
import { z } from 'zod';
import { protectedProcedure, router } from '../core';

// NOTE: Raw key only returned on creation (not stored). We store sha256 hash.

export const apiKeyRouter = router({
  list: protectedProcedure.query(async ({ ctx }) => {
    const userId = ctx.user?.id;
    if (!userId) return [];
    const space = await getOrCreateUserSpace(userId);
    const rows = await ctx.prisma.apiKey.findMany({
      where: { spaceId: space.id },
      orderBy: { createdAt: 'desc' },
      select: { id: true, name: true, createdAt: true, revokedAt: true },
    });
    return rows;
  }),
  create: protectedProcedure
    .input(z.object({ name: z.string().min(1).max(120).default('key') }))
    .mutation(async ({ ctx, input }) => {
      const raw = crypto.randomBytes(24).toString('hex');
      const hash = crypto.createHash('sha256').update(raw).digest('hex');
      const userId = ctx.user?.id;
      if (!userId) throw new Error('Unauthorized');
      const space = await getOrCreateUserSpace(userId);
      const created = await ctx.prisma.apiKey.create({
        data: {
          id: generateNanoID('sk-'),
          name: input.name || 'key',
          tokenHash: hash,
          spaceId: space.id,
        },
        select: { id: true, name: true, createdAt: true },
      });
      return { ...created, raw };
    }),
  revoke: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.apiKey.update({ where: { id: input.id }, data: { revokedAt: new Date() } });
      return { id: input.id, revoked: true };
    }),
});
