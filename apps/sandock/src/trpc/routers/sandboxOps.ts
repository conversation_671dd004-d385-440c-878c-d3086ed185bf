import { persistentSandboxManager } from '@sandock/domains/sandbox/sandbox-manager';
import { getOrCreateUserSpace } from '@sandock/domains/sandbox/space';
import type { SandboxCodeOptions, SandboxShellOptions } from '@sandock/domains/sandbox/types';
import { TRPCError } from '@trpc/server';
import { observable } from '@trpc/server/observable';
import { generateNanoID } from 'basenext/utils';
import { z } from 'zod';
import { protectedProcedure, router } from '../core';

// Operations on an existing sandbox instance (runtime level)
export const sandboxOpsRouter = router({
  heartbeat: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      const space = await getOrCreateUserSpace(ctx.user.id);
      const sb = await ctx.prisma.sandbox.findFirst({
        where: { id: input.id, spaceId: space.id },
      });
      if (!sb) throw new TRPCError({ code: 'NOT_FOUND' });
      // Heartbeat: ensure provider running lazily
      try {
        await persistentSandboxManager.getProvider(sb.id);
        await persistentSandboxManager.heartbeat(sb.id);
      } catch {}
      return ctx.prisma.sandbox.update({
        where: { id: sb.id },
        data: { lastHeartbeatAt: new Date() },
      });
    }),
  active: protectedProcedure.query(async ({ ctx }) => {
    const items = await ctx.prisma.sandbox.findMany({
      where: { deletedAt: null },
      orderBy: { createdAt: 'desc' },
      take: 50,
    });
    return { items };
  }),
  runCode: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        language: z.enum(['javascript', 'typescript', 'python']),
        code: z.string().min(1),
        timeoutMs: z.number().int().positive().max(120_000).optional(),
        input: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      const space = await getOrCreateUserSpace(ctx.user.id);
      const sb = await ctx.prisma.sandbox.findFirst({
        where: { id: input.id, spaceId: space.id },
      });
      if (!sb) throw new TRPCError({ code: 'NOT_FOUND' });
      if (sb.status !== 'RUNNING')
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Sandbox not running' });
      if (!sb.providerRef)
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Missing providerRef' });
      try {
        const provider = await persistentSandboxManager.getProvider(sb.id);
        const opts: SandboxCodeOptions = {
          language: input.language,
          code: input.code,
          timeoutMs: input.timeoutMs,
          input: input.input,
        };
        const res = await provider.runCode(opts);
        // log (truncate large fields)
        await ctx.prisma.sandboxExecutionLog.create({
          data: {
            id: generateNanoID('sel'),
            sandboxId: sb.id,
            userId: ctx.user.id,
            type: 'RUN_CODE',
            language: input.language,
            codeSnippet: input.code.slice(0, 2000),
            exitCode: res.exitCode ?? undefined,
            durationMs: res.durationMs,
            stdoutPreview: res.stdout.slice(0, 1000),
            stderrPreview: res.stderr.slice(0, 1000),
          },
        });
        return res;
      } catch {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'runCode failed' });
      }
    }),
  shell: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        cmd: z.union([z.string(), z.array(z.string().min(1)).min(1)]),
        timeoutMs: z.number().int().positive().max(60_000).optional(),
        workdir: z.string().optional(),
        env: z.record(z.string()).optional(),
        input: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      const space = await getOrCreateUserSpace(ctx.user.id);
      const sb = await ctx.prisma.sandbox.findFirst({
        where: { id: input.id, spaceId: space.id },
      });
      if (!sb) throw new TRPCError({ code: 'NOT_FOUND' });
      if (sb.status !== 'RUNNING')
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Sandbox not running' });
      if (!sb.providerRef)
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Missing providerRef' });
      try {
        const provider = await persistentSandboxManager.getProvider(sb.id);
        const opts: SandboxShellOptions = {
          cmd: input.cmd,
          timeoutMs: input.timeoutMs,
          workdir: input.workdir,
          env: input.env,
          input: input.input,
        };
        const res = await provider.shell(opts);
        await ctx.prisma.sandboxExecutionLog.create({
          data: {
            id: generateNanoID('sel'),
            sandboxId: sb.id,
            userId: ctx.user.id,
            type: 'SHELL',
            command: Array.isArray(input.cmd) ? input.cmd.join(' ') : input.cmd,
            exitCode: res.exitCode ?? undefined,
            durationMs: res.durationMs,
            stdoutPreview: res.stdout.slice(0, 1000),
            stderrPreview: res.stderr.slice(0, 1000),
          },
        });
        return res;
      } catch {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'shell failed' });
      }
    }),
  writeFile: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        path: z.string().min(1),
        content: z.string(),
        executable: z.boolean().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      const space = await getOrCreateUserSpace(ctx.user.id);
      const sb = await ctx.prisma.sandbox.findFirst({
        where: { id: input.id, spaceId: space.id },
      });
      if (!sb) throw new TRPCError({ code: 'NOT_FOUND' });
      if (sb.status !== 'RUNNING')
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Sandbox not running' });
      if (!sb.providerRef)
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Missing providerRef' });
      try {
        const provider = await persistentSandboxManager.getProvider(sb.id);
        await provider.writeFile({
          path: input.path,
          content: input.content,
          executable: input.executable,
        });
        return true;
      } catch {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'writeFile failed' });
      }
    }),
  readFile: protectedProcedure
    .input(z.object({ id: z.string(), path: z.string().min(1) }))
    .query(async ({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      const space = await getOrCreateUserSpace(ctx.user.id);
      const sb = await ctx.prisma.sandbox.findFirst({
        where: { id: input.id, spaceId: space.id },
      });
      if (!sb) throw new TRPCError({ code: 'NOT_FOUND' });
      if (!sb.providerRef)
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Missing providerRef' });
      try {
        const provider = await persistentSandboxManager.getProvider(sb.id);
        const content = await provider.readFile(input.path);
        return { path: input.path, content };
      } catch {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'readFile failed' });
      }
    }),
  listFiles: protectedProcedure
    .input(z.object({ id: z.string(), path: z.string().optional() }))
    .query(async ({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      const space = await getOrCreateUserSpace(ctx.user.id);
      const sb = await ctx.prisma.sandbox.findFirst({
        where: { id: input.id, spaceId: space.id },
      });
      if (!sb) throw new TRPCError({ code: 'NOT_FOUND' });
      if (!sb.providerRef)
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Missing providerRef' });
      try {
        const provider = await persistentSandboxManager.getProvider(sb.id);
        const entries = await provider.list(input.path);
        return { path: input.path ?? '.', entries };
      } catch {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'listFiles failed' });
      }
    }),
  removePath: protectedProcedure
    .input(z.object({ id: z.string(), path: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      const space = await getOrCreateUserSpace(ctx.user.id);
      const sb = await ctx.prisma.sandbox.findFirst({
        where: { id: input.id, spaceId: space.id },
      });
      if (!sb) throw new TRPCError({ code: 'NOT_FOUND' });
      if (!sb.providerRef)
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'Missing providerRef' });
      try {
        const provider = await persistentSandboxManager.getProvider(sb.id);
        await provider.remove(input.path);
        return true;
      } catch {
        throw new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'removePath failed' });
      }
    }),
  logs: protectedProcedure
    .input(
      z.object({
        sandboxId: z.string().optional(),
        type: z.enum(['RUN_CODE', 'SHELL']).optional(),
        page: z.number().int().min(1).default(1),
        pageSize: z.number().int().min(1).max(100).default(20),
      }),
    )
    .query(async ({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      const space = await getOrCreateUserSpace(ctx.user.id);
      const where: Record<string, unknown> = { sandbox: { spaceId: space.id } };
      if (input.sandboxId) where.sandboxId = input.sandboxId; // still allow narrowing by sandbox id
      if (input.type) where.type = input.type;
      const [total, items] = await Promise.all([
        ctx.prisma.sandboxExecutionLog.count({ where }),
        ctx.prisma.sandboxExecutionLog.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip: (input.page - 1) * input.pageSize,
          take: input.pageSize,
        }),
      ]);
      const totalPages = Math.max(1, Math.ceil(total / input.pageSize));
      return { items, total, page: input.page, pageSize: input.pageSize, totalPages };
    }),
  // Experimental streaming endpoints (requires WS link client-side)
  runCodeStream: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        language: z.enum(['javascript', 'typescript', 'python']),
        code: z.string().min(1),
        timeoutMs: z.number().int().positive().max(120_000).optional(),
        input: z.string().optional(),
      }),
    )
    .subscription(({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      return observable<
        | { type: 'stdout' | 'stderr'; data: string }
        | { type: 'exit'; exitCode: number | null; timedOut: boolean; durationMs: number }
      >((emit) => {
        let cancelled = false;
        (async () => {
          const space = await getOrCreateUserSpace(ctx.user.id);
          const sb = await ctx.prisma.sandbox.findFirst({
            where: { id: input.id, spaceId: space.id },
          });
          if (!sb || sb.status !== 'RUNNING' || !sb.providerRef) {
            emit.error(new TRPCError({ code: 'BAD_REQUEST', message: 'Sandbox not running' }));
            return;
          }
          try {
            const provider = (await persistentSandboxManager.getProvider(sb.id)) as unknown as {
              runCodeStream?: (
                opts: SandboxCodeOptions,
              ) => AsyncIterable<
                | { type: 'stdout'; data: string }
                | { type: 'stderr'; data: string }
                | { type: 'exit'; exitCode: number | null; timedOut: boolean; durationMs: number }
              >;
            };
            if (!provider.runCodeStream) {
              emit.error(
                new TRPCError({ code: 'NOT_IMPLEMENTED', message: 'Streaming not supported' }),
              );
              return;
            }
            for await (const ev of provider.runCodeStream({
              language: input.language,
              code: input.code,
              timeoutMs: input.timeoutMs,
              input: input.input,
            })) {
              if (cancelled) break;
              emit.next(ev);
            }
            emit.complete();
          } catch {
            emit.error(
              new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'runCodeStream failed' }),
            );
          }
        })();
        return () => {
          cancelled = true;
        };
      });
    }),
  shellStream: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        cmd: z.union([z.string(), z.array(z.string().min(1)).min(1)]),
        timeoutMs: z.number().int().positive().max(60_000).optional(),
        workdir: z.string().optional(),
        env: z.record(z.string()).optional(),
        input: z.string().optional(),
      }),
    )
    .subscription(({ ctx, input }) => {
      if (!ctx.user) throw new TRPCError({ code: 'UNAUTHORIZED' });
      return observable<
        | { type: 'stdout' | 'stderr'; data: string }
        | { type: 'exit'; exitCode: number | null; timedOut: boolean; durationMs: number }
      >((emit) => {
        let cancelled = false;
        (async () => {
          const space = await getOrCreateUserSpace(ctx.user.id);
          const sb = await ctx.prisma.sandbox.findFirst({
            where: { id: input.id, spaceId: space.id },
          });
          if (!sb || sb.status !== 'RUNNING' || !sb.providerRef) {
            emit.error(new TRPCError({ code: 'BAD_REQUEST', message: 'Sandbox not running' }));
            return;
          }
          try {
            const provider = (await persistentSandboxManager.getProvider(sb.id)) as unknown as {
              shellStream?: (
                opts: SandboxShellOptions,
              ) => AsyncIterable<
                | { type: 'stdout'; data: string }
                | { type: 'stderr'; data: string }
                | { type: 'exit'; exitCode: number | null; timedOut: boolean; durationMs: number }
              >;
            };
            if (!provider.shellStream) {
              emit.error(
                new TRPCError({ code: 'NOT_IMPLEMENTED', message: 'Streaming not supported' }),
              );
              return;
            }
            for await (const ev of provider.shellStream({
              cmd: input.cmd,
              timeoutMs: input.timeoutMs,
              workdir: input.workdir,
              env: input.env,
              input: input.input,
            })) {
              if (cancelled) break;
              emit.next(ev);
            }
            emit.complete();
          } catch {
            emit.error(
              new TRPCError({ code: 'INTERNAL_SERVER_ERROR', message: 'shellStream failed' }),
            );
          }
        })();
        return () => {
          cancelled = true;
        };
      });
    }),
});

export type SandboxOpsRouter = typeof sandboxOpsRouter;
