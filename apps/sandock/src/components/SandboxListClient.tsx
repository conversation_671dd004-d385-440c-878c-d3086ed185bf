'use client';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import Card from '@mui/joy/Card';
import Chip from '@mui/joy/Chip';
import Input from '@mui/joy/Input';
import Sheet from '@mui/joy/Sheet';
import Stack from '@mui/joy/Stack';
import Tooltip from '@mui/joy/Tooltip';
import Typography from '@mui/joy/Typography';
import { useState } from 'react';
import { sandboxStatusColor } from '../app/dashboard/sandboxes/statusColor';
import { trpc } from '../trpc/react';
import { ClientTerminalButton } from './ClientTerminalButton';

export function SandboxListClient() {
  const [page, setPage] = useState(1);
  const [q, setQ] = useState('');
  const [title, setTitle] = useState('');
  // Provider now determined solely by server via SANDBOX_PROVIDER env; UI no longer selects it.

  const listQuery = trpc.sandbox.list.useQuery({ page, q: q || undefined });
  const createMut = trpc.sandbox.create.useMutation({
    onSuccess: () => {
      setTitle('');
      listQuery.refetch();
      countQuery.refetch();
    },
  });
  const startMut = trpc.sandbox.start.useMutation({ onSuccess: () => listQuery.refetch() });
  const stopMut = trpc.sandbox.stop.useMutation({ onSuccess: () => listQuery.refetch() });
  const deleteMut = trpc.sandbox.delete.useMutation({ onSuccess: () => listQuery.refetch() });
  const countQuery = trpc.sandbox.count.useQuery();

  const varId = (
    v: typeof startMut.variables | typeof stopMut.variables | typeof deleteMut.variables,
  ) => (v && typeof v === 'object' && 'id' in v ? (v as { id?: string }).id : undefined);

  const totalPages = listQuery.data?.totalPages ?? 1;

  return (
    <Stack spacing={3}>
      <Stack spacing={2}>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
          <Typography level="h3" sx={{ fontWeight: 600, flex: 1 }}>
            Sandboxes{' '}
            {countQuery.data && (
              <Chip size="sm" variant="soft">
                {countQuery.data.sandboxCount}
              </Chip>
            )}
          </Typography>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              createMut.mutate({ title: title || 'New Sandbox' });
            }}
            style={{ display: 'flex', gap: 8, flexDirection: 'row', flexWrap: 'wrap' }}
          >
            <Input
              name="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="新建 Sandbox 标题"
              size="sm"
              sx={{ minWidth: 160 }}
            />
            <Button type="submit" size="sm" variant="solid" loading={createMut.isPending}>
              创建
            </Button>
          </form>
        </Box>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            setPage(1);
            listQuery.refetch();
          }}
          style={{ display: 'flex', gap: 8 }}
        >
          <Input
            name="q"
            placeholder="搜索标题..."
            size="sm"
            value={q}
            onChange={(e) => setQ(e.target.value)}
            sx={{ maxWidth: 280 }}
          />
          <Button size="sm" type="submit" variant="outlined">
            搜索
          </Button>
        </form>
      </Stack>

      {listQuery.isLoading ? (
        <Sheet variant="outlined" sx={{ p: 4, textAlign: 'center', borderRadius: 'sm' }}>
          <Typography level="body-sm">加载中...</Typography>
        </Sheet>
      ) : listQuery.data && listQuery.data.items.length === 0 ? (
        <Sheet variant="outlined" sx={{ p: 4, textAlign: 'center', borderRadius: 'sm' }}>
          <Typography level="title-md" sx={{ mb: 1 }}>
            暂无 Sandboxes
          </Typography>
          <Typography level="body-sm" sx={{ color: 'text.secondary' }}>
            之后可以在这里快速创建和管理你的沙盒环境。
          </Typography>
        </Sheet>
      ) : (
        <Stack spacing={2}>
          {(listQuery.data?.items ?? []).map((sb: unknown) => {
            interface SandboxLite {
              id: string;
              title: string;
              createdAt: string | number | Date;
              status: string;
              provider: string;
            }
            const sandbox = sb as SandboxLite;
            return (
              <Card
                key={sandbox.id}
                variant="outlined"
                sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', sm: 'row' },
                  alignItems: { sm: 'center' },
                  gap: 1.5,
                }}
              >
                <Box sx={{ flex: 1 }}>
                  <Typography level="title-md" sx={{ fontWeight: 600 }}>
                    {sandbox.title}
                  </Typography>
                  <Typography level="body-xs" sx={{ color: 'text.tertiary', mt: 0.5 }}>
                    创建时间 {new Date(sandbox.createdAt).toISOString()}
                  </Typography>
                </Box>
                <Stack direction="row" spacing={1} sx={{ alignItems: 'center' }}>
                  <Chip size="sm" variant="soft" color="neutral">
                    ID: {sandbox.id.slice(0, 8)}
                  </Chip>
                  <Chip size="sm" variant="soft" color={sandboxStatusColor(sandbox.status)}>
                    {sandbox.status}
                  </Chip>
                  <Chip
                    size="sm"
                    variant="soft"
                    color={
                      sandbox.provider === 'DOCKER'
                        ? 'warning'
                        : sandbox.provider === 'KUBERNETES'
                          ? 'primary'
                          : 'neutral'
                    }
                  >
                    {sandbox.provider}
                  </Chip>
                  {sandbox.status !== 'RUNNING' &&
                    sandbox.status !== 'DELETING' &&
                    sandbox.status !== 'DELETED' && (
                      <Tooltip title="启动">
                        <Button
                          size="sm"
                          variant="outlined"
                          color="success"
                          loading={startMut.isPending && varId(startMut.variables) === sandbox.id}
                          onClick={() => startMut.mutate({ id: sandbox.id })}
                        >
                          启动
                        </Button>
                      </Tooltip>
                    )}
                  {sandbox.status === 'RUNNING' && (
                    <Tooltip title="停止">
                      <Button
                        size="sm"
                        variant="outlined"
                        color="warning"
                        loading={stopMut.isPending && varId(stopMut.variables) === sandbox.id}
                        onClick={() => stopMut.mutate({ id: sandbox.id })}
                      >
                        停止
                      </Button>
                    </Tooltip>
                  )}
                  {sandbox.status === 'RUNNING' && <ClientTerminalButton sandboxId={sandbox.id} />}
                  {sandbox.status !== 'DELETING' && sandbox.status !== 'DELETED' && (
                    <Tooltip title="删除 (不可恢复)">
                      <Button
                        size="sm"
                        variant="plain"
                        color="danger"
                        loading={deleteMut.isPending && varId(deleteMut.variables) === sandbox.id}
                        onClick={() => {
                          if (confirm('确认要删除该 Sandbox? 此操作不可恢复。')) {
                            deleteMut.mutate({ id: sandbox.id });
                          }
                        }}
                      >
                        删除
                      </Button>
                    </Tooltip>
                  )}
                </Stack>
              </Card>
            );
          })}
        </Stack>
      )}

      {totalPages > 1 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, gap: 1, flexWrap: 'wrap' }}>
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
            <Button
              key={p}
              size="sm"
              variant={p === page ? 'solid' : 'plain'}
              color={p === page ? 'primary' : 'neutral'}
              onClick={() => setPage(p)}
            >
              {p}
            </Button>
          ))}
        </Box>
      )}
    </Stack>
  );
}
