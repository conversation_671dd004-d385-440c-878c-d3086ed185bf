# COPY INTO .env.local

# NEXT_PUBLIC_ prefix is used to expose the environment variable to the browser!!!

# 访问地址
APP_HOSTNAME=http://localhost:3000
APP_ENV=LOCAL

# 协同文档websocket服务, 构建时候已经设置好了, 本地开发才需要设置
DOC_SERVER=ws://localhost:3366

# Post Hog (Dev环境)未启用
# NEXT_PUBLIC_POSTHOG_KEY=phc_t8QSxDCl5Z9qSavno6UKpstTB1LtMsHXrDLTJo0vesB
# NEXT_PUBLIC_POSTHOG_HOST=https://eu.posthog.com

# 私有化版本必须默认设置这个变量并且构建进去私有化镜像版本
# NEXT_PUBLIC_SELF_HOST=true

# 管理License Server的list/create/view/delete才用到，在线激活服务则不需要鉴权
# LICENSE_SERVER_ADMIN_KEY=WMDkgEGbKHkCFm6KK

# 私有化版本授权码，填写后会校验授权码是否正确, 走网络激活服务
# LICENSE_CODE=

DEFAULT_LOCALE=en

# 你自己本地启动的数据库服务器
PG_DATABASE_URL="postgresql://bika:bikabika@127.0.0.1:5432/bikadev?schema=public&connection_limit=1"
MONGODB_DATABASE_URL="*******************************************************************************"
REDIS_URL='redis://default:@127.0.0.1:6379'
QDRANT_URL='http://localhost:6333'
MINIO_URL="minio://bika:bikabika@127.0.0.1:9000/bikadev?useSSL=false&autoCreateBucket=true"
STORAGE_PUBLIC_URL="http://127.0.0.1:9000/bikadev"

# 开发环境(私有云)的数据库服务器
# PG_DATABASE_URL="postgresql://bika:<EMAIL>:15432/bikadev?schema=public&connection_limit=1"
# MONGODB_DATABASE_URL="mongodb://bika:<EMAIL>:27017/bikadev?authSource=admin&replicaSet=rs0"
# MINIO_URL="r2://3e368e72cb8cbc3f8aaea901866046ac:<EMAIL>/bika-dev?useSSL=true"

# Elastic Search 业务数据索引日志，不记录console log，支持空配置，则disable Elastic Search发送
# ***************************:PORT/PREFIX
# ELASTIC_SEARCH_URL=***********************************/bika-dev
# ELASTIC_SEARCH_URL=https://SjI7nJEBYaC0d4wPOz9z:<EMAIL>/bika-dev
ELASTIC_SEARCH_URL=***********************************/bika-local
OPENOBSERVE_LOG_URL=***********************************************/bika-local

# EDGE TOKEN，内部服务、跨服务调h用的token，边缘函数调用API服务器时，双方一致才通过
EDGE_TOKEN=FRCbZ7abmHWXuo6h9VKhz9wXuVjgx7TTSWxpVJGzyVks0ExI2tpoQuz0qWRMrvRw

ENABLE_POSTGRES_LOGGING=
ENABLE_MONGODB_LOGGING=

# SMS 启动类型。不填写则不启用
SMS_TYPE= # tencent | ...
# Tencent Cloud SMS
TENCENTCLOUD_SECRET_ID=
TENCENTCLOUD_SECRET_KEY=
TENCENTCLOUD_REGION=
TENCENTCLOUD_SMS_APP_ID=
TENCENTCLOUD_SMS_SIGN_NAME=
TENCENTCLOUD_SMS_VERIFICATION_TEMPLATE_ID=

# smtp | resend | aws-ses
# 开发环境建议 smtp，填入自己的SMTP key做验证
# SYSTEM_EMAIL_PROVIDER='{"provider": "smtp", "host": "smtp.gmail.com", "username": "<EMAIL>", "password": "xzhhhcrxfhqbsoxo", "port": 587}'
# SERVICE_EMAIL_PROVIDER='{"provider": "smtp", "host": "smtp.gmail.com", "username": "<EMAIL>", "password": "xzhhhcrxfhqbsoxo", "port": 587}'
# SERVICE_EMAIL_PROVIDER='{"provider":"aws-ses", "region":"us-east-1","accessKeyId":"KEY","secretAccessKey":"KEY","senderMail": "<EMAIL>"}'
SYSTEM_EMAIL_PROVIDER=
SERVICE_EMAIL_PROVIDER=

# 这里是只配OpenAI兼容的key，base URL请在ai-model-config.ts中配置
OPENAI_API_KEY=
OPENAI_BASE_URL='https://openai.bika.ltd/v1'

# 系统调用默认什么模型，不填默认qwen2(ollama.bika.hk)
SYSTEM_AI_MODEL=

LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=
# 系统调用默认什么模型，不填默认qwen2(ollama.bika.hk)
AI_MODEL=
# AI_MODEL=gpt-4o-mini
# AI_MODEL=doubao
# AI_MODEL=qwen2
COPILOT_AKI_AI_MODEL=deepseek/deepseek-r1
COPILOT_EDIT_AI_MODEL=openai/gpt-4.1
IGNORE_AI_CREDIT_ENOUGH=false

# 是否开启测试里的AI测试testing，会跑OpenAI接口/Resend邮件服务和各种integration第三方集成
TEST_AI=false
# 开启MOCK_AI，则尽量使用本地mock的AI数据，包括tools等，方便本地单测
MOCK_AI=true
# 是否开启SDK测试，填入API服务器的baseURL时才开始，不填则不启用
# TEST_API_SDK_SERVER=http://localhost:3000/api/openapi
TEST_API_SDK_SERVER=

# VIKA数据源导入测试
VIKA_MYSQL_HOST="127.0.0.1"
VIKA_MYSQL_PORT="3306"
VIKA_MYSQL_USERNAME="root"
VIKA_MYSQL_PASSWORD="root"
VIKA_MYSQL_DATABASE="vika"
VIKA_DATABASE_TABLE_PREFIX="vika_"

# GitHub OAuth
GITHUB_CLIENT_ID="********************"
GITHUB_CLIENT_SECRET="0f9d934efe2049587219cf66f25ea47a0246f34f"

# 国内显示备案号 host
CN_HOST=bikaai.cn

# 在这里创建 https://console.cloud.google.com/apis/credentials
GOOGLE_CLIENT_ID="383974913215-065o57h9rl8q6en173lk3ob2841g75tc.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"

# FIREBASE
FIREBASE_API_KEY="AIzaSyByCyP8rH6uydZkuWtHWEGEWbhhgLGYkqg"
FIREBASE_AUTH_DOMAIN="bika-project.firebaseapp.com"
FIREBASE_PROJECT_ID="bika-project"
FIREBASE_STORAGE_BUCKET="bika-project.appspot.com"
FIREBASE_MESSAGING_SENDER_ID="896517107811"
FIREBASE_APP_ID="1:896517107811:web:ab89cc7a5c6dddbbcac856"

FIREBASE_CLIENT_EMAIL="<EMAIL>"
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
# vapidKey
FIREBASE_PUBLIC_VAPID_KEY="BKbyQqWXsyYcAwcyBNXErUig0g1yjVlQNUNKnc-dKp6LEsdhpEcgLmJmsosY3RaJOoPXEx6gKY1CaHb_gkFcw1I"

# Stripe
STRIPE_PUBLIC_KEY=pk_test_51O2XWcLGyXnYfGi6lKGtQIyo3BAXH84FF08XAnmh7OEtzhTy0Joyd6srVefDLFYxzyK7r7tG5aJ0W4muLVchi9Lf00NHJSL5g7
STRIPE_SECRET_KEY=sk_test_51O2XWcLGyXnYfGi6Ci7KbKMYT5qNS3NeZAVbnA2628JpYOiByO6L1xFury2cuC9HcYF1fpwopUNyLf2Qm9ZCUL8k00NUpc3o8J

# Stripe Webhook for https://dev.bika.ai/api/stripe/webhooks
STRIPE_WEBHOOK_SECRET=whsec_VbFKm0sB2DShgZJ5gv2BQoj2wRtyppIj

# 服务器时间锁定UTC
TZ=GMT

UNSPLASH_ACCESS_KEY="uxNvKE4x7PlIw-vBpJv8ppA2WMoym5Z6yCeIFv2Xi6o"

# 微信登录 本地用测试号 需要关注我的号 要把微信消息接口设置到 /api/auth/callback/weixin
WEIXIN_APP_ID=wx87f708b16c1b11a4
WEIXIN_APP_SECRET=c6d321e455accc2e3f9097a9bb1393e2
WEIXIN_MESSAGE_TOKEN=weixin

NODE_OPTIONS=--max-old-space-size=10240


# Formapp AI
FORMAPP_AI_API_KEY=sk6sfEJNkX8rKG4LPPmp9NAMN3a5C1chx2
FORMAPP_AI_BASE_URL=https://dev.formapp.ai/api/openapi
# FORMAPP_AI_BASE_URL=http://localhost:3001/api/openapi
# ToolSDK AI
TOOLSDK_AI_API_KEY=skGbyDAJW8qv8LvpOKVtlGqHp9GhbME4Sb
TOOLSDK_AI_BASE_URL=https://toolsdk.ai/api/openapi
# TOOLSDK_AI_API_KEY=skW32Z6wzHSLVNN9nXLNNANeo8keuxBEIY
# TOOLSDK_AI_BASE_URL=https://dev.toolsdk.ai/api/openapi
# TOOLSDK_AI_BASE_URL=http://localhost:3002/api/openapi

TOOLSDK_REGISTRY_URL=https://registry-dev.toolsdk.ai

# appsumo
APPSUMO_CLIENT_ID=1
APPSUMO_CLIENT_SECRET=test
APPSUMO_PRIVATE_KEY=test

# 是否开启性能监控，会产生大量日志
PERF_HOOK=false

# Discousre 社区登录
DISCOURSE_SSO_SECRET=
DISCOURSE_SSO_URL=https://community.bika.ai


# skillset
BRAVE_API_KEY=BSAb-P0gUyxwU4nn4eq3CqcIZR9U903
TAVILY_API_KEY=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
EXA_API_KEY=3892b447-afac-49cd-9dd0-6617e0a42f7c