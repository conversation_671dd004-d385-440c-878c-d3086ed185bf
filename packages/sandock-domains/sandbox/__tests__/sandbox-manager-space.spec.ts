import { beforeAll, describe, expect, it } from 'vitest';
import { prisma } from '../../prisma/db';
import { persistentSandboxManager } from '../sandbox-manager';
import { getOrCreateUserSpace } from '../space';

const TEST_USER_ID = 'test-user-space-api';

async function ensureUser(id: string) {
  await prisma.user.upsert({
    where: { id },
    update: {},
    create: { id, email: id + '@example.com', clerkId: 'clerk_' + id },
  });
}

describe('SandboxManager spaceId create path', () => {
  beforeAll(async () => {
    await ensureUser(TEST_USER_ID);
  });

  it('creates via explicit spaceId', async () => {
    const space = await getOrCreateUserSpace(TEST_USER_ID);
    const sb = await persistentSandboxManager.create({
      spaceId: space.id,
      actorUserId: TEST_USER_ID,
      title: 'space-direct',
      forceProvider: 'LOCAL',
    });
    expect(sb.spaceId).toBe(space.id);
  });
});
