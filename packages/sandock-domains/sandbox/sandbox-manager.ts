import type { SandboxProvider as DbSandboxProvider, Sandbox } from '@sandock/domains/prisma';
import { SandboxStatus } from '@sandock/domains/prisma';
import { generateNanoID } from 'basenext/utils';
import { prisma } from '../prisma/db';
import type { $Enums, Prisma } from '../prisma/prisma-client';
import { closeRunSegment, recordStartSuccess, recordStatusEvent } from './metrics';
import { createSandboxProvider } from './sandbox';
import type { SandboxProvider } from './types';

/** Input when creating a sandbox */
export interface CreateSandboxInput {
  /** Target space id (required). */
  spaceId: string;
  /** Optional actor user (for future multi-member auditing). */
  actorUserId?: string;
  title: string;
  forceProvider?: DbSandboxProvider;
  image?: string;
  cpuLimit?: number;
  memoryLimit?: number;
  metadata?: Record<string, unknown>;
}

export interface PersistentSandboxManagerOptions {
  /** Base directory for local provider deterministic folders (optional) */
  localBaseDir?: string;
}

// Provider selection now mapped via env to Persistent manager provider enum names
const providerName: DbSandboxProvider =
  (process.env.SANDBOX_PROVIDER?.toLowerCase() as DbSandboxProvider) || 'DOCKER';

/**
 * A DB-backed sandbox manager (no in-memory cache) – each call fetches DB state
 * and constructs a provider instance on demand. Suitable as a first step to
 * persistence; later you can layer an L1 cache & reconciliation loop.
 */
export class SandboxManager {
  private localBaseDir?: string;

  constructor(opts: PersistentSandboxManagerOptions = {}) {
    this.localBaseDir = opts.localBaseDir;
  }

  /** Create sandbox row then start provider & update status */
  async create(input: CreateSandboxInput): Promise<Sandbox> {
    const providerEnum: $Enums.SandboxProvider = input.forceProvider || providerName;
    const targetSpaceId = input.spaceId;
    // Initial DB row (status CREATING)
    const row = await prisma.sandbox.create({
      data: {
        id: generateNanoID('sdb'),
        spaceId: targetSpaceId,
        title: input.title,
        provider: providerEnum,
        status: SandboxStatus.CREATING,
        image: input.image,
        cpuLimit: input.cpuLimit ?? null,
        memoryLimit: input.memoryLimit ?? null,
        metadata: (input.metadata as Prisma.InputJsonValue) ?? undefined,
      },
    });
    await recordStatusEvent({
      sandboxId: row.id,
      type: 'CREATED',
      toStatus: SandboxStatus.CREATING,
    });
    try {
      const provider = this.instantiateProvider(row, { image: input.image });
      await recordStatusEvent({ sandboxId: row.id, type: 'START_ATTEMPT' });
      const providerRef = await provider.start();
      const updated = await prisma.sandbox.update({
        where: { id: row.id },
        data: {
          status: SandboxStatus.RUNNING,
          providerRef: providerRef,
          lastHeartbeatAt: new Date(),
          statusChangedAt: new Date(),
        },
      });
      await recordStartSuccess(row.id, row.createdAt, false);
      await recordStatusEvent({
        sandboxId: row.id,
        fromStatus: SandboxStatus.CREATING,
        toStatus: SandboxStatus.RUNNING,
      });
      return updated;
    } catch (e) {
      await prisma.sandbox.update({
        where: { id: row.id },
        data: {
          status: SandboxStatus.ERROR,
          metadata: { ...(row.metadata as object), error: String(e) },
          statusChangedAt: new Date(),
        },
      });
      await recordStatusEvent({
        sandboxId: row.id,
        fromStatus: SandboxStatus.CREATING,
        toStatus: SandboxStatus.ERROR,
        type: 'START_FAIL',
        meta: { error: String(e) },
      });
      throw e;
    }
  }

  /** Get sandbox row (throws if not found or deleted) */
  async get(id: string): Promise<Sandbox> {
    const row = await prisma.sandbox.findUnique({ where: { id } });
    if (!row) throw new Error('Sandbox not found');
    if (row.deletedAt) throw new Error('Sandbox deleted');
    return row;
  }

  /** Ensure a provider instance for given sandbox id. Starts if necessary. */
  async getProvider(id: string): Promise<SandboxProvider> {
    const row = await this.get(id);
    const provider = this.instantiateProvider(row, { image: row.image || undefined });
    if (row.status !== SandboxStatus.RUNNING) {
      try {
        await recordStatusEvent({
          sandboxId: id,
          type: 'START_ATTEMPT',
          fromStatus: row.status,
          toStatus: SandboxStatus.RUNNING,
        });
        const ref = await provider.start();
        await prisma.sandbox.update({
          where: { id },
          data: {
            status: SandboxStatus.RUNNING,
            providerRef: ref,
            lastHeartbeatAt: new Date(),
            statusChangedAt: new Date(),
          },
        });
        await recordStartSuccess(id, row.createdAt, true);
        await recordStatusEvent({
          sandboxId: id,
          fromStatus: row.status,
          toStatus: SandboxStatus.RUNNING,
        });
      } catch (e) {
        await prisma.sandbox.update({
          where: { id },
          data: {
            status: SandboxStatus.ERROR,
            metadata: { ...(row.metadata as object), restartError: String(e) },
            statusChangedAt: new Date(),
          },
        });
        await recordStatusEvent({
          sandboxId: id,
          fromStatus: row.status,
          toStatus: SandboxStatus.ERROR,
          type: 'RESTART_FAIL',
          meta: { error: String(e) },
        });
        throw e;
      }
    }
    return provider;
  }

  /** Stop provider resources and mark STOPPED */
  async stop(id: string): Promise<void> {
    const row = await this.get(id);
    if (row.status === SandboxStatus.DELETED || row.deletedAt) return;
    const provider = this.instantiateProvider(row, { image: row.image || undefined });
    try {
      await provider.start(); // ensure handle if needed for stop logic (docker/local rely on active reference discovery by name/id)
    } catch {}
    try {
      await provider.stop();
    } catch (e) {
      // record error but continue
      await prisma.sandbox.update({
        where: { id },
        data: { metadata: { ...(row.metadata as object), stopError: String(e) } },
      });
    }
    await prisma.sandbox.update({
      where: { id },
      data: { status: SandboxStatus.STOPPED, statusChangedAt: new Date() },
    });
    await closeRunSegment(id, 'STOPPED');
    await recordStatusEvent({
      sandboxId: id,
      fromStatus: row.status,
      toStatus: SandboxStatus.STOPPED,
      type: 'STOP',
    });
  }

  /** Soft delete -> mark DELETING -> stop -> mark DELETED */
  async delete(id: string): Promise<void> {
    const row = await this.get(id);
    if (row.deletedAt) return;
    await prisma.sandbox.update({
      where: { id },
      data: { status: SandboxStatus.DELETING, statusChangedAt: new Date() },
    });
    await recordStatusEvent({
      sandboxId: id,
      fromStatus: row.status,
      toStatus: SandboxStatus.DELETING,
      type: 'DELETE_ATTEMPT',
    });
    try {
      await this.stop(id);
    } finally {
      await prisma.sandbox.update({
        where: { id },
        data: { status: SandboxStatus.DELETED, deletedAt: new Date(), statusChangedAt: new Date() },
      });
      await recordStatusEvent({
        sandboxId: id,
        fromStatus: SandboxStatus.DELETING,
        toStatus: SandboxStatus.DELETED,
        type: 'DELETED',
      });
    }
  }

  /** Heartbeat updates lastHeartbeatAt */
  async heartbeat(id: string): Promise<void> {
    await prisma.sandbox.update({ where: { id }, data: { lastHeartbeatAt: new Date() } });
    await recordStatusEvent({ sandboxId: id, type: 'HEARTBEAT' });
  }

  /** List sandboxes (filter by space + optional status). */
  async list(opts: { spaceId: string; status?: SandboxStatus[] }): Promise<Sandbox[]> {
    return prisma.sandbox.findMany({
      where: {
        spaceId: opts.spaceId,
        status: opts.status ? { in: opts.status } : undefined,
        deletedAt: null,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /** Instantiate provider based on DB row (no caching). */
  private instantiateProvider(row: Sandbox, extra: { image?: string }): SandboxProvider {
    const name = row.provider;
    switch (name) {
      case 'DOCKER':
        return createSandboxProvider('DOCKER', { id: row.id, image: extra.image, pull: true });
      case 'LOCAL':
        return createSandboxProvider('LOCAL', { id: row.id, workdir: this.localBaseDir });
      case 'KUBERNETES':
        return createSandboxProvider('KUBERNETES', {} as never);
      default:
        throw new Error(`Unsupported provider ${name}`);
    }
  }
}

export const persistentSandboxManager = new SandboxManager();
