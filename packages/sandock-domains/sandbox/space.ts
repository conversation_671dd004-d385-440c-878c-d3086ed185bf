import { generateNanoID } from 'basenext/utils';
import { prisma } from '../prisma/db';

/**
 * Get or create (1:1) personal space for a user. Future: extend for multi-space membership.
 */
export async function getOrCreateUserSpace(userId: string) {
  const existing = await prisma.space.findUnique({ where: { ownerId: userId } });
  if (existing) return existing;
  return prisma.space.create({ data: { id: generateNanoID('spc'), ownerId: userId } });
}
