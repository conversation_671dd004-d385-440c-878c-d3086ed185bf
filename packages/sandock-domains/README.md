# Sandock Domains – Sandbox Module

沙箱执行子系统（支持 Local / Docker / 预留 Kubernetes）+ 生命周期指标与计费埋点。已从直接按 `userId` 归属，升级为通过 **Space**（协作空间）归属资源。

## 关键模型
* Space: 工作空间（当前一人一 Space，后续可多人协作）。`ownerId` 唯一约束保证 1:1。 
* Sandbox: 归属 Space；状态机 + 运行时段段（RunSegment）累积 CPU 时间。
* SandboxExecutionLog / LifecycleEvent: 审计与状态事件。

## 创建 / 获取
```ts
import { persistentSandboxManager } from '@sandock/domains/sandbox/sandbox-manager';

// manager.create 传入 userId（内部自动确保 user 的 Space 存在）
const sb = await persistentSandboxManager.create({
	userId: 'user-123',
	title: 'demo',
	forceProvider: 'DOCKER',
	image: 'node:20-bullseye'
});

// 后续获取 / 自启动
const live = await persistentSandboxManager.get(sb.id);

// 列表：可用 spaceId 或 userId（二选一）
await persistentSandboxManager.list({ userId: 'user-123' });
```

## Space 兼容策略
现阶段 API 仍接受 `userId`（自动映射到其 Space）以减小改动。未来协作启用后：
* 对外新增 `spaceId` 作为首选标识
* `userId` 仅用于获取“默认个人空间”

## 计费与运行时指标
RunSegment 记录 RUNNING 连续区间；关闭（STOP / RECONCILE / ERROR）时累计 `accumulatedRunMs`，供计费模块查询。

## 下一步（未来）
* SpaceMember 表（多成员）
* 权限 role / invite token
* 资源配额按 Space 聚合

