import { afterAll, beforeAll, describe, expect, it } from 'vitest';
import { PLAN_DEFS } from '../../billing/plan-config';
import { computeOverageUSD, UnitPriceConfig } from '../../billing/pricing-config';
import { prisma } from '../../prisma/db';
import { closeRunSegment, openRunSegment } from '../../sandbox/metrics';
import { persistentSandboxManager } from '../../sandbox/sandbox-manager';
import { getOrCreateUserSpace } from '../../sandbox/space';

const TEST_USER_ID = 'test-user-billing';

async function ensureTestUser() {
  await prisma.user.upsert({
    where: { id: TEST_USER_ID },
    update: {},
    create: {
      id: TEST_USER_ID,
      email: `${TEST_USER_ID}@example.com`,
      clerkId: `clerk_${TEST_USER_ID}`,
      name: 'Test User',
    },
  });
}

function seconds(ms: number) {
  return ms / 1000;
}
// NOTE: Requires valid PG_DATABASE_URL with accessible Postgres. After Space refactor
// CI env currently lacks real credentials; keep skipped until test harness provides DB.
describe('Sandbox run -> billing integration (SandboxManager)', () => {
  let sandboxId: string;

  // Use local provider to avoid slow docker image pulls in CI; extend hook timeout as safety.
  beforeAll(async () => {
    await ensureTestUser();
    const space = await getOrCreateUserSpace(TEST_USER_ID);
    const sb = await persistentSandboxManager.create({
      spaceId: space.id,
      actorUserId: TEST_USER_ID,
      title: 'manager-sb',
      forceProvider: 'LOCAL', // faster, no container startup needed for billing accumulation tests
    });
    sandboxId = sb.id;
  }, 120_000);

  afterAll(async () => {
    await prisma.sandbox.delete({ where: { id: sandboxId } }).catch(() => {});
  });

  it('accumulates runtime within quota w/ zero overage', async () => {
    const quota = PLAN_DEFS.HOBBY.quota;
    const start = new Date();
    await openRunSegment(sandboxId, start);
    const end = new Date(start.getTime() + 60 * 60 * 1000); // 1h
    await closeRunSegment(sandboxId, 'normal-stop', end);
    const sb = await prisma.sandbox.findUnique({ where: { id: sandboxId } });
    if (!sb) throw new Error('sandbox missing after segment');
    // Allow small timing drift (DB/store rounding)
    expect(Math.abs(sb.accumulatedRunMs - 60 * 60 * 1000)).toBeLessThan(50);
    const usage = { cpuSeconds: seconds(sb.accumulatedRunMs), memorySeconds: 0, storageSeconds: 0 };
    expect(usage.cpuSeconds).toBeLessThan(quota.cpuSeconds);
    const cost = computeOverageUSD('HOBBY', usage);
    expect(cost).toBe(0);
  });

  it('charges only exceeded seconds after going past quota (cpu)', async () => {
    const quota = PLAN_DEFS.HOBBY.quota;
    const current = await prisma.sandbox.findUnique({ where: { id: sandboxId } });
    if (!current) throw new Error('sandbox missing before exceed');
    const remaining = quota.cpuSeconds * 1000 - current.accumulatedRunMs;
    const start = new Date();
    await openRunSegment(sandboxId, start);
    const overMs = remaining + 30_000; // exceed by 30s
    await closeRunSegment(sandboxId, 'normal-stop', new Date(start.getTime() + overMs));
    const sb = await prisma.sandbox.findUnique({ where: { id: sandboxId } });
    if (!sb) throw new Error('sandbox missing after exceed');
    const usageSeconds = seconds(sb.accumulatedRunMs);
    const overSeconds = usageSeconds - quota.cpuSeconds;
    expect(overSeconds).toBeGreaterThanOrEqual(30);
    const cost = computeOverageUSD('HOBBY', {
      cpuSeconds: usageSeconds,
      memorySeconds: 0,
      storageSeconds: 0,
    });
    const expected = overSeconds * UnitPriceConfig.cpu;
    expect(Number(cost.toFixed(6))).toBe(Number(expected.toFixed(6)));
  });

  it('enterprise (no metered) rejects over-quota runtime logic (simulated)', async () => {
    const usage = { cpuSeconds: 10, memorySeconds: 0, storageSeconds: 0 };
    const def = PLAN_DEFS.ENTERPRISE;
    expect(def.amountCents).toBeNull();
    // business rule: cannot auto-meter enterprise
    function compute() {
      if (def.amountCents === null) throw new Error('enterprise-unmetered');
      return computeOverageUSD('ENTERPRISE', usage);
    }
    expect(compute).toThrowError(/enterprise-unmetered/);
  });
});
