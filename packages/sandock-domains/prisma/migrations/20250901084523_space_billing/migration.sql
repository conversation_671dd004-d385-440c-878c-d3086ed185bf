/*
  Warnings:

  - You are about to drop the column `userId` on the `Api<PERSON>ey` table. All the data in the column will be lost.
  - You are about to drop the `UserBilling` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `spaceId` to the `ApiKey` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "ApiKey" DROP CONSTRAINT "ApiKey_userId_fkey";

-- DropForeignKey
ALTER TABLE "UserBilling" DROP CONSTRAINT "UserBilling_userId_fkey";

-- DropIndex
DROP INDEX "ApiKey_userId_idx";

-- AlterTable
ALTER TABLE "ApiKey" DROP COLUMN "userId",
ADD COLUMN     "spaceId" TEXT NOT NULL;

-- DropTable
DROP TABLE "UserBilling";

-- CreateTable
CREATE TABLE "SpaceBilling" (
    "id" TEXT NOT NULL,
    "spaceId" TEXT NOT NULL,
    "plan" "BillingPlan" NOT NULL DEFAULT 'HOBBY',
    "stripeCustomerId" TEXT,
    "stripeSubscriptionId" TEXT,
    "currentPeriodEnd" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SpaceBilling_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "SpaceBilling_spaceId_key" ON "SpaceBilling"("spaceId");

-- CreateIndex
CREATE INDEX "SpaceBilling_plan_idx" ON "SpaceBilling"("plan");

-- CreateIndex
CREATE INDEX "ApiKey_spaceId_idx" ON "ApiKey"("spaceId");

-- AddForeignKey
ALTER TABLE "ApiKey" ADD CONSTRAINT "ApiKey_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "Space"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SpaceBilling" ADD CONSTRAINT "SpaceBilling_spaceId_fkey" FOREIGN KEY ("spaceId") REFERENCES "Space"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
