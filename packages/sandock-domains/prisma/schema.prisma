// Sandock Domains Prisma Schema
// Shared across sandock domain logic and Next.js app.

generator client {
  provider      = "prisma-client-js"
  output        = "./prisma-client"
  binaryTargets = ["native", "debian-openssl-3.0.x", "linux-musl-openssl-3.0.x", "debian-openssl-1.1.x", "linux-musl"]
}

datasource db {
  provider = "postgresql"
  url      = env("PG_DATABASE_URL")
}

model User {
  id        String    @id
  email     String    @unique
  name      String?
  imageUrl  String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  clerkId   String    @unique
  // Relation: one (current constraint) or many spaces owned / participated in future
  spaces    Space[]
  sandboxExecutionLogs SandboxExecutionLog[]
  // billing moved to SpaceBilling (1:1 with Space)
}

enum SandboxProvider {
  DOCKER
  KUBERNETES
  LOCAL
}

enum SandboxStatus {
  CREATING
  RUNNING
  STOPPED
  ERROR
  DELETING
  DELETED
}

enum SandboxExecutionType {
  RUN_CODE
  SHELL
}

// Fine-grained lifecycle event types for SandboxLifecycleEvent.type
enum SandboxLifecycleEventType {
  CREATED
  START_ATTEMPT
  START_SUCCESS
  START_FAIL
  RESTART_FAIL
  STATUS_CHANGE
  STOP
  DELETE_ATTEMPT
  DELETED
  HEARTBEAT
  RECONCILE_STOP
  RETENTION_DELETE
  RUN_SEGMENT_END
}

model Sandbox {
  id             String          @id
  // Now belongs to a Space instead of directly to a User
  space          Space           @relation(fields: [spaceId], references: [id])
  spaceId        String
  title          String
  provider       SandboxProvider @default(DOCKER)
  providerRef    String?         // Container ID / Pod name / Local path
  status         SandboxStatus   @default(CREATING)
  image          String?
  cpuLimit       Int?
  memoryLimit    Int?
  metadata       Json?
  lastHeartbeatAt DateTime?
  deletedAt      DateTime?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  // Metrics / lifecycle enrichment (added in migration add_sandbox_metrics)
  firstStartedAt        DateTime?
  lastStartedAt         DateTime?
  lastStoppedAt         DateTime?
  accumulatedRunMs      Int            @default(0) // sum of closed run segments
  lastStartupDurationMs Int?
  statusChangedAt       DateTime?      // when entered current status

  executions SandboxExecutionLog[]
  runSegments SandboxRunSegment[]
  lifecycleEvents SandboxLifecycleEvent[]

  @@index([spaceId])
  @@index([status])
}

model ApiKey {
  id         String   @id
  space      Space    @relation(fields: [spaceId], references: [id])
  spaceId    String
  name       String
  tokenHash  String
  lastUsedAt DateTime?
  expiresAt  DateTime?
  revokedAt  DateTime?
  createdAt  DateTime @default(now())

  @@index([spaceId])
}

model SandboxExecutionLog {
  id           String                @id
  sandbox      Sandbox               @relation(fields: [sandboxId], references: [id])
  sandboxId    String
  user         User                  @relation(fields: [userId], references: [id])
  userId       String
  type         SandboxExecutionType
  language     String?
  command      String?
  codeSnippet  String?               // truncated code for auditing
  exitCode     Int?
  durationMs   Int?
  stdoutPreview String?
  stderrPreview String?
  createdAt    DateTime              @default(now())

  @@index([sandboxId])
  @@index([userId])
  @@index([type])
}

// Sandbox lifecycle fine-grained events (state transitions, start attempts, reconcile changes, etc.)
model SandboxLifecycleEvent {
  id         String   @id
  sandbox    Sandbox  @relation(fields: [sandboxId], references: [id])
  sandboxId  String
  type       SandboxLifecycleEventType // previously String
  fromStatus SandboxStatus?
  toStatus   SandboxStatus?
  ts         DateTime @default(now())
  meta       Json?

  @@index([sandboxId, ts])
  @@index([type])
}

// Continuous RUNNING segments. One open segment has endAt NULL.
model SandboxRunSegment {
  id         String   @id
  sandbox    Sandbox  @relation(fields: [sandboxId], references: [id])
  sandboxId  String
  startAt    DateTime
  endAt      DateTime?
  endReason  String?  // STOPPED | ERROR | RECONCILE | DELETE
  durationMs Int?

  @@index([sandboxId, startAt])
  @@index([endAt])
}

// Billing plan enum
enum BillingPlan {
  HOBBY
  PRO
  ENTERPRISE
}

// Billing per Space (1:1). OwnerId uniqueness on Space enforces indirect per-user uniqueness for now.
model SpaceBilling {
  id                   String      @id @default(cuid())
  space                Space       @relation(fields: [spaceId], references: [id])
  spaceId              String      @unique
  plan                 BillingPlan @default(HOBBY)
  stripeCustomerId     String?
  stripeSubscriptionId String?
  currentPeriodEnd     DateTime?
  createdAt            DateTime    @default(now())
  updatedAt            DateTime    @updatedAt

  @@index([plan])
}

// Workspace / collaboration scope (initially 1:1 with User, later multi-member)
model Space {
  id        String    @id
  owner     User      @relation(fields: [ownerId], references: [id])
  ownerId   String    @unique // temporary constraint: one space per user

  name      String? // If empty, user owner's name as space's name
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  sandboxes Sandbox[]
  billing   SpaceBilling?
  apiKeys   ApiKey[]

  @@index([ownerId])
}
