/**
 * MongoDB Mongoose Models for Database
 */
import mongoose, { type InferSchemaType } from 'mongoose';

/**
 * 回收站，我们不涉及「软删除」的技术方式，而是直接删除，但是把删除的数据放到「回收站」，用户可以看到！
 *
 * 定期进行回收站的数据删除清理，MongoDB单表Collection支持海量数据
 */
export const TrashSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true },

    createdBy: { type: String },
    updatedBy: { type: String }, // user id

    // 哪个数据库表模型被删了？
    model: {
      type: String,
      required: true,
      enum: ['DATABASE', 'DATABASE_RECORD', 'NODE_RESOURCE'],
    },

    // 它的ID是？
    modelId: { type: String, required: true },

    // 放入个性化的索引值，比如，回收database records时，可以放入{spaceId, databaseId}等以方便寻找
    modelIndex: { type: Object },

    // 它完整的数据放这吧
    modelData: { type: Object, required: true },
  },
  {
    timestamps: true,
  },
);

export type TrashModel = InferSchemaType<typeof TrashSchema>;

export const TrashDAO = mongoose.models.Trash || mongoose.model('Trash', TrashSchema);
