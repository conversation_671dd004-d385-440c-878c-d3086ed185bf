/**
 * MongoDB Mongoose Models for Database
 */

import mongoose, { type InferSchemaType } from 'mongoose';

/**
 * 记录发出去的Email
 */
export const EmailSchema = new mongoose.Schema(
  {
    // emailId
    id: { type: String, required: true, index: true },

    // string或string[]
    to: { type: mongoose.Schema.Types.Mixed, required: true },

    // 标题
    subject: { type: String },

    // HTML缓存
    body: { type: String },

    // 是否发送成功
    success: { type: Boolean, required: true },

    // 发送邮件供应商的返回结果标记
    messageId: { type: String },

    type: { type: String },
    spaceId: { type: String, index: true },
    // 关联的通知ID/ActionId/...
    relationId: { type: String, index: true },

    // 发送邮件的其它信息
    metadata: { type: mongoose.Schema.Types.Mixed },

    // track标记，用于统计打开率、回复率
    track: { type: Number, required: true, default: 0 },

    createdBy: { type: String },
    updatedBy: { type: String }, // user id
  },
  {
    timestamps: true,
  },
);

export type EmailModel = InferSchemaType<typeof EmailSchema>;
export const EmailDAO = mongoose.models.Email || mongoose.model('Email', EmailSchema);
