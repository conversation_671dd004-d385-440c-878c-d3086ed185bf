/**
 * MongoDB Mongoose Models for Database
 */

import { TalkBOSchema } from '@bika/types/space/bo';
import { RecipientPropsSchema } from '@bika/types/system/recipient';
import { modNanoId } from 'basenext/utils';
import mongoose, { type InferSchemaType } from 'mongoose';
import { getAppEnv } from 'sharelib/app-env';

/**
 * AI Intent Wizard Dialog
 */
export const TalkSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true },

    createdBy: { type: String, index: true },
    updatedBy: { type: String }, // user id

    recipient: {
      type: Object,
      required: true,
      validate: {
        validator: (v: object) => {
          RecipientPropsSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid RecipientPropsSchema: ${JSON.stringify(props)}`,
      },
    },
    // BO
    data: {
      type: Object,
      required: true,
      validate: {
        validator: (v: object) => {
          TalkBOSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid FeedBOSchema: ${JSON.stringify(props)}`,
      },
    },
    count: { type: Number, default: 0 },
    // 是否置顶
    isPinned: { type: Boolean, default: false },
  },
  {
    timestamps: true,
  },
);

export type TalkModel = InferSchemaType<typeof TalkSchema>;

export function TalkDAO(
  indexName: string | bigint,
  type: 'INDEX' | 'RECIPIENT_ID' = 'RECIPIENT_ID',
) {
  let collectionHashNum = indexName;
  if (type === 'RECIPIENT_ID') {
    // LOCAL  开发模式，单个 collection 方便调试
    const maxCollectionCount = getAppEnv() === 'LOCAL' ? 1 : 128;
    collectionHashNum = modNanoId(indexName as string, maxCollectionCount);
  }
  const daoName = `Talk_${collectionHashNum}`;

  const dao: mongoose.Model<TalkModel> =
    mongoose.models[daoName] || mongoose.model(daoName, TalkSchema);
  return dao;
}
