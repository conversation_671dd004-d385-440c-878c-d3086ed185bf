/**
 * MongoDB Mongoose Models for Database
 */

import mongoose, { type InferSchemaType } from 'mongoose';

/**
 * Comment
 */
export const CommentSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true },
    createdBy: { type: String },
    updatedBy: { type: String }, // user id
    // isDeleted: { type: Boolean, require: true, default: false },

    // 关联对象类型
    relationType: { type: String, required: true, enum: ['RECORD', 'MISSION'] },
    // 关联对象的编号
    relationId: { type: String, required: true },

    /**
     * 富文本内容
     */
    content: { type: String, required: true },

    // 通常是UnitMember发出, 也有可能是站外用户,所以这里是非必填
    unitId: { type: String, index: true },

    // 冗余
    spaceId: { type: String, required: true },
  },
  {
    timestamps: true,
  },
);

export type CommentModel = InferSchemaType<typeof CommentSchema>;
export const CommentDAO = mongoose.models.Comment || mongoose.model('Comment', CommentSchema);
