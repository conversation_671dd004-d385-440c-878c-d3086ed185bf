import * as mongoose from 'mongoose';
import { GlobalMongoosePlugin } from './global-plugin';

mongoose.plugin(GlobalMongoosePlugin);

import { RecipientRelationTypeEnumSchema } from '@bika/types/system';
import { AsyncLocalStorage } from 'async_hooks';
import { AIArtifactDAO } from './ai-artifact-model';
import { AIChatDAO } from './ai-chat-model';
import { AICompletionDAO } from './ai-completion-model';
import { AIIntegrationChatDAO } from './ai-integration-chat-model';
import { AIMessageDAO } from './ai-message-model';
import { AIToolDAO } from './ai-tool-model';
import { AutomationRunHistoryDAO } from './automation-run-history-model';
import { CommentDAO } from './comment-model';
import { EmailInvitationDAO } from './email-invitation-model';
import { EmailDAO } from './email-model';
import { GiftCodeDAO } from './gift-code-model';
import { MissionDAO } from './mission-model';
import { NotificationDAO } from './notification-model';
import { recipientDAO } from './recipient-model';
// import { UserLicenseDAO } from './user-license-model';
import { DatabaseRecordChangeLogDAO } from './record-change-log-model';
import { DatabaseRecordLinkDAO } from './record-link-model';
import { DatabaseRecordDAO } from './record-model';
import { ReminderDAO } from './reminder-model';
import { RemoteStorageDAO } from './remote-storage-model';
import { ReportDAO } from './report-model';
import { SearchIndexDAO } from './search-index-model';
import { SpaceAttachmentDAO } from './space-attachment-model';
import { SubscriptionReportCursorDAO } from './subscription-report-cursor';
import { SubscriptionStateCheckCursorDAO } from './subscription-state-check-cursor';
import { SurveyDAO } from './survey-model';
import { TalkDAO } from './talk-model';
import { TmpAttachmentDAO } from './tmp-attachment-model';
import { TrashDAO } from './trash-model';
import { UsageLogDAO } from './usage-log-model';
import { UsageStatDAO } from './usage-stat-model';
import { UserReferralCodeDAO } from './user-referral-code-model';

const mongoUrl = process.env.MONGODB_DATABASE_URL as string; // 'mongodb://localhost:27017';

export type MongoTransactionCB<T = void> = (session: mongoose.ClientSession) => Promise<T>;

export type MongoTransactionCBOptional<T = void> = (session?: mongoose.ClientSession) => Promise<T>;

export { mongoose };

/**
 * MongoDB DAOs.
 */
export class MongoClient {
  private static _isInited = false;

  private static mongoDB: mongoose.mongo.Db | undefined;

  private static sessionLocalStorage = new AsyncLocalStorage<mongoose.mongo.ClientSession>();

  public static initOnce() {
    if (!MongoClient._isInited) {
      if (process.env.ENABLE_MONGODB_LOGGING === 'true') {
        mongoose.set('debug', true);
      }

      // 请使用 db-seed 来管理索引, 避免生产环境中出现问题
      mongoose.set('autoIndex', false);

      mongoose
        .connect(mongoUrl, {
          autoIndex: false,
          // autoCreate: false,
        })
        .then((db) => {
          MongoClient.mongoDB = db.connection.db;
          // FIX CI test
          // ref: https://stackoverflow.com/questions/54327147/mongoerror-unable-to-acquire-lock
          if (process.env.CI) {
            db.connection.db?.admin().command({
              setParameter: 1,
              maxTransactionLockRequestTimeoutMillis: 3000,
            });
          }
        });

      MongoClient._isInited = true;
    }
  }

  public static currentSession(): mongoose.ClientSession | null {
    return MongoClient.sessionLocalStorage.getStore() ?? null;
  }

  static async transaction<T>(
    callback: MongoTransactionCB<T>,
    options?: mongoose.mongo.TransactionOptions,
  ): Promise<T> {
    // Start a new session
    const session = await mongoose.startSession({
      defaultTransactionOptions: options,
    });
    try {
      // Start a transaction
      session.startTransaction();
      // Execute the callback and pass the session
      const result = await callback(session);
      // If everything is OK, commit the transaction
      await session.commitTransaction();

      return result;
    } catch (error) {
      // If something goes wrong, abort the transaction
      if (session.inTransaction()) {
        await session.abortTransaction();
      }

      throw error;
    } finally {
      // End the session
      await session.endSession();
    }
  }

  static get aiIntegrationChat() {
    return AIIntegrationChatDAO;
  }

  static get automationRunHistory() {
    return AutomationRunHistoryDAO;
  }

  static get aiChat() {
    return AIChatDAO;
  }

  static get aiArtifact() {
    return AIArtifactDAO;
  }

  static get aiTool() {
    return AIToolDAO;
  }

  static get comment() {
    return CommentDAO;
  }

  static get mission() {
    return MissionDAO;
  }

  static get userReferralCode() {
    return UserReferralCodeDAO;
  }

  static get aiCompletion() {
    return AICompletionDAO;
  }

  static get databaseRecordLink() {
    return DatabaseRecordLinkDAO;
  }

  static get databaseRecordChangeLog() {
    return DatabaseRecordChangeLogDAO;
  }

  static get notification() {
    return NotificationDAO;
  }

  static get tmpAttachment() {
    return TmpAttachmentDAO;
  }

  static get spaceAttachment() {
    return SpaceAttachmentDAO;
  }

  static get reminder() {
    return ReminderDAO;
  }

  static get report() {
    return ReportDAO;
  }

  /**
   * 回收站，能回收任何数据模型
   */
  static get trash() {
    return TrashDAO;
  }

  /**
   * Local Storage, Session Storage服务器版，存放零碎key-value数据
   */
  static get remoteStorage() {
    return RemoteStorageDAO;
  }

  /**
   * Search Index
   */
  static get searchIndex() {
    return SearchIndexDAO;
  }

  /**
   * 灵活的调查问卷
   */
  static get survey() {
    return SurveyDAO;
  }

  /**
   * 灵活的调查问卷
   */
  static get email() {
    return EmailDAO;
  }

  static get emailInvitation() {
    return EmailInvitationDAO;
  }

  static get giftCode() {
    return GiftCodeDAO;
  }

  static get usageLog() {
    return UsageLogDAO;
  }

  static get usageStat() {
    return UsageStatDAO;
  }

  static get subscriptionReportCursor() {
    return SubscriptionReportCursorDAO;
  }

  static get subscriptionStateCheckCursor() {
    return SubscriptionStateCheckCursorDAO;
  }

  // 以下是分表

  static get aiMessage() {
    return AIMessageDAO;
  }

  static get talk() {
    return TalkDAO;
  }

  /**
   * 同一个model、同一个dao，但是放到不同的document collection，类似分表，方便性能查询
   */
  static get recipient() {
    return recipientDAO;
  }

  static get databaseRecord() {
    return DatabaseRecordDAO;
  }

  /**
   * DatabaseRecord的索引管理
   */
  private static async syncDatabaseRecordIndexes() {
    console.log('Sync DatabaseRecord Indexes');
    // 列出所有DatabaseRecord_开头的collections
    const collections = await MongoClient.mongoDB
      ?.listCollections({ name: { $regex: /^DatabaseRecord_/i } })
      .toArray();
    const collectionNames = collections?.map((collection: { name: string }) => collection.name);

    // 遍历所有匹配的集合
    for (const collectionName of collectionNames ?? []) {
      try {
        const index = collectionName.split('_')[1];
        const { toDrop, toCreate } = await MongoClient.databaseRecord(
          index,
          'HASH_ID',
        ).diffIndexes();
        if (toDrop.length > 0 || toCreate.length > 0) {
          console.log(
            `Sync DatabaseRecord Indexes: ${collectionName}, toDrop: ${JSON.stringify(toDrop)}, toCreate: ${JSON.stringify(toCreate)}`,
          );
          await MongoClient.databaseRecord(index, 'HASH_ID').syncIndexes();
        } else {
          console.log(`No need to sync DatabaseRecord Indexes: ${collectionName}`);
        }
      } catch (error) {
        console.error(`Error Sync DatabaseRecord collection Indexes ${collectionName}:`, error);
      }
    }
  }

  private static async syncRecipientIndexes() {
    console.log('Sync Recipient Indexes');
    // 遍历所有匹配的集合
    for (const recipientType of RecipientRelationTypeEnumSchema.options) {
      try {
        const { toDrop, toCreate } = await MongoClient.recipient(recipientType).diffIndexes();
        if (toDrop.length > 0 || toCreate.length > 0) {
          console.log(
            `Sync Recipient Indexes: ${recipientType}, toDrop: ${JSON.stringify(toDrop)}, toCreate: ${JSON.stringify(toCreate)}`,
          );
          await MongoClient.recipient(recipientType).syncIndexes();
        } else {
          console.log(`No need to sync Recipient Indexes: ${recipientType}`);
        }
      } catch (error) {
        console.error(`Error Sync Recipient collection Indexes ${recipientType}:`, error);
      }
    }
  }

  /**
   * AI Message的索引管理
   */
  private static async syncAiMessageIndexes() {
    console.log('Sync AI Message Indexes');
    // 列出所有AI Message相关的collections
    const collections = await MongoClient.mongoDB
      ?.listCollections({ name: { $regex: /^AIMessage_/i } })
      .toArray();
    const collectionNames = collections?.map((collection: { name: string }) => collection.name);

    // 遍历所有匹配的集合
    for (const collectionName of collectionNames ?? []) {
      try {
        const index = collectionName.split('_')[2];
        const { toDrop, toCreate } = await MongoClient.aiMessage(index, 'INDEX').diffIndexes();
        if (toDrop.length > 0 || toCreate.length > 0) {
          console.log(
            `Sync AI Message Indexes: ${collectionName}, toDrop: ${JSON.stringify(toDrop)}, toCreate: ${JSON.stringify(toCreate)}`,
          );
          await MongoClient.aiMessage(index, 'INDEX').syncIndexes();
        } else {
          console.log(`No need to sync AI Message Indexes: ${collectionName}`);
        }
      } catch (error) {
        console.error(`Error Sync AI Message collection Indexes ${collectionName}:`, error);
      }
    }
  }

  /**
   * Talk的索引管理
   */
  private static async syncTalkIndexes() {
    console.log('Sync Talk Indexes');
    // 列出所有Talk相关的collections
    const collections = await MongoClient.mongoDB
      ?.listCollections({ name: { $regex: /^Talk_/i } })
      .toArray();
    const collectionNames = collections?.map((collection: { name: string }) => collection.name);

    // 遍历所有匹配的集合
    for (const collectionName of collectionNames ?? []) {
      try {
        const index = collectionName.split('_')[2];
        const { toDrop, toCreate } = await MongoClient.talk(index, 'INDEX').diffIndexes();
        if (toDrop.length > 0 || toCreate.length > 0) {
          console.log(
            `Sync Talk Indexes: ${collectionName}, toDrop: ${JSON.stringify(toDrop)}, toCreate: ${JSON.stringify(toCreate)}`,
          );
          await MongoClient.talk(index, 'INDEX').syncIndexes();
        } else {
          console.log(`No need to sync Talk Indexes: ${collectionName}`);
        }
      } catch (error) {
        console.error(`Error Sync Talk collection Indexes ${collectionName}:`, error);
      }
    }
  }

  private static async syncIndexes(model: mongoose.Model<any>) {
    const { toDrop, toCreate } = await model.diffIndexes();
    console.log(
      `Mongo Model: ${model.modelName}, toDrop: ${JSON.stringify(toDrop)}, toCreate: ${JSON.stringify(toCreate)}`,
    );
    if (toDrop.length > 0 || toCreate.length > 0) {
      console.log(`Sync ${model.modelName} Indexes:`, [toDrop, toCreate]);
      await model.syncIndexes();
    }
  }

  static async migrateIndexes() {
    await MongoClient.syncIndexes(MongoClient.aiIntegrationChat);
    await MongoClient.syncIndexes(MongoClient.automationRunHistory);
    await MongoClient.syncIndexes(MongoClient.aiChat);
    await MongoClient.syncIndexes(MongoClient.aiArtifact);
    await MongoClient.syncIndexes(MongoClient.email);
    await MongoClient.syncIndexes(MongoClient.aiTool);
    await MongoClient.syncIndexes(MongoClient.comment);
    await MongoClient.syncIndexes(MongoClient.mission);
    await MongoClient.syncIndexes(MongoClient.userReferralCode);
    await MongoClient.syncIndexes(MongoClient.aiCompletion);
    await MongoClient.syncIndexes(MongoClient.databaseRecordLink);
    await MongoClient.syncIndexes(MongoClient.databaseRecordChangeLog);
    await MongoClient.syncIndexes(MongoClient.notification);
    await MongoClient.syncIndexes(MongoClient.tmpAttachment);
    await MongoClient.syncIndexes(MongoClient.spaceAttachment);
    await MongoClient.syncIndexes(MongoClient.reminder);
    await MongoClient.syncIndexes(MongoClient.report);
    await MongoClient.syncIndexes(MongoClient.trash);
    await MongoClient.syncIndexes(MongoClient.remoteStorage);
    await MongoClient.syncIndexes(MongoClient.searchIndex);
    await MongoClient.syncIndexes(MongoClient.survey);
    await MongoClient.syncIndexes(MongoClient.emailInvitation);
    await MongoClient.syncIndexes(MongoClient.giftCode);
    await MongoClient.syncIndexes(MongoClient.usageLog);
    await MongoClient.syncIndexes(MongoClient.usageStat);
    await MongoClient.syncIndexes(MongoClient.subscriptionReportCursor);
    await MongoClient.syncIndexes(MongoClient.subscriptionStateCheckCursor);

    // 同步DatabaseRecord的索引
    await MongoClient.syncDatabaseRecordIndexes();
    // 同步Recipient的索引
    await MongoClient.syncRecipientIndexes();
    // 同步AIMessage的索引
    await MongoClient.syncAiMessageIndexes();
    // 同步Talk的索引
    await MongoClient.syncTalkIndexes();
  }
}
