/**
 * MongoDB Mongoose Models for Database
 */

import { AIChatShareSchema, type AIIntentParams, AIIntentParamsSchema } from '@bika/types/ai/bo';
import mongoose, { type InferSchemaType } from 'mongoose';

/**
 * Yes, AIIntentVO = AIIntentPO(model)
 */
export type AIIntentModel = AIIntentParams;

/**
 * AI Intent Wizard Dialog DB Chat PO
 */
export const AIChatSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true },
    createdBy: { type: String, index: true },
    updatedBy: { type: String }, // user id
    // isDeleted: { type: Boolean, require: true, default: false },

    /**
    // Wizard Dialog跟着空间站的member
    // 可能为空，即匿名用户了
     * @deprecated
     */
    // unitId: { type: String, required: true },

    // 角色, 可以是个人用户身份、空间站成员等，role play
    roleType: { type: String, enum: ['USER', 'MEMBER'], required: true },
    roleId: { type: String, required: true },

    // 对话的消息, Message BO
    /**
     * @deprecated 改用 AIMessageModel 了
     */
    // messages: { type: Array, default: [] },

    // 当前的意图对象，及其参数 // AIIntentModel = AIIntentVO
    // 另外有 Intent UI VO，这个是动态根据当前填充的参数、状态，返回给客户端的UI，不进行持久化
    intent: {
      type: Object,
      required: true,
      validate: {
        validator: (v: object) => {
          AIIntentParamsSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid AIIntentParamsSchema : ${JSON.stringify(props)}`,
      },
    },

    // 意图识别的状态 // IntentResolutionStatus，独立状态，是因为有时候哪怕参数已经填满了（Resolve），但还需要用户confirm
    intentResolutionStatus: { type: String },

    // 是否已读。UI界面新建，默认为已读；通过 action 创建为未读，（对话全空间站共享）任何一人进入对话，都会标记为已读
    read: { type: Boolean },
    metadata: { type: mongoose.Schema.Types.Mixed },

    // 标题和描述，由 AI 生成，用于历史回放
    title: { type: String },
    description: { type: String },
    // 分享信息
    share: {
      type: Object,
      validate: {
        validator: (v: object) => {
          AIChatShareSchema.parse(v);
          return true;
        },
        message: (props: mongoose.ValidatorProps) =>
          `${props.value} is not valid AIChatShareSchema : ${JSON.stringify(props)}`,
      },
    },
  },
  {
    timestamps: true,
  },
);

// 稀疏索引，只对存在 nodeId 的文档建立索引
AIChatSchema.index({ 'intent.nodeId': 1 }, { sparse: true });

// 稀疏索引，只对存在 spaceId 的文档建立索引
AIChatSchema.index({ 'intent.spaceId': 1 }, { sparse: true });

// 稀疏索引，只对存在 spaceId 的文档建立索引
AIChatSchema.index({ 'intent.copilot.nodeId': 1 }, { sparse: true });

export type AIChatModel = InferSchemaType<typeof AIChatSchema>;

export const AIChatDAO = mongoose.models.AIChat || mongoose.model('AIChat', AIChatSchema);
