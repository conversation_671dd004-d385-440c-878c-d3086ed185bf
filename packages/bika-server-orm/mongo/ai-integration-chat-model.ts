/**
 * MongoDB Mongoose Models for Database
 */

import mongoose, { type InferSchemaType } from 'mongoose';

/**
 * 集成聊天，telegram等对话过程
 */
export const AIIntegrationChatSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true },
    createdBy: { type: String },
    updatedBy: { type: String }, // user id

    // 集成聊天类型
    type: { type: String, enum: ['TELEGRAM'], require: true },

    // Chat的User ID，如telegram user，注意，一个chat group内，会有多个user
    // userId: { type: String, required: true },

    // Chat ID，通常是集成的聊天软件的ID
    chatId: { type: String, required: true },

    // 外部集成的chat，依旧会映射一个ai wizard
    aiWizardId: { type: String, required: true },
  },
  {
    // 5分钟内，1个chat对应持续对应一个wizard，超过5分钟，wizard重置
    timestamps: true,
  },
);

export type AIIntegrationChatModel = InferSchemaType<typeof AIIntegrationChatSchema>;

export const AIIntegrationChatDAO =
  mongoose.models.AIIntegrationChat || mongoose.model('AIIntegrationChat', AIIntegrationChatSchema);
