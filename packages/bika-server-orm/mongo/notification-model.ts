/**
 * MongoDB Mongoose Models for Database
 */

import { NotificationPropertySchema } from '@bika/types/notification/bo';
import mongoose, { type InferSchemaType } from 'mongoose';

/**
 * Notification
 *
 * Report是给Member的，Notification是给User的，Notification有可能是一个Report的引
 */
export const NotificationSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true },

    // 对应Enum type，NotificationType
    type: { type: String, index: true, required: true },

    // Notification是根据property，自动获取内容
    property: {
      type: mongoose.Schema.Types.Mixed,
      required: true,
      default: {},

      validate: {
        validator: (v: unknown) => {
          NotificationPropertySchema.parse(v);
          return true;
        },
        message: (props: { value: string }) =>
          `${props.value} is not valid NotificationIndex : ${JSON.stringify(props)}`,
      },
    },

    createdBy: { type: String },
    updatedBy: { type: String }, // user id
  },
  {
    timestamps: true,
  },
);

export type NotificationModel = InferSchemaType<typeof NotificationSchema>;
export const NotificationDAO =
  mongoose.models.Notification || mongoose.model('Notification', NotificationSchema);
