import { RecipientRelationTypeEnumSchema } from '@bika/types/system';
import mongoose from 'mongoose';
import { db } from '../../db';
import { PrismaClient } from '../prisma-client';
import { seedBlogs } from './seed-blogs';
import { seedHelps } from './seed-helps';
import { seedLandingPages } from './seed-landing-pages';
import { seedTemplates } from './seed-templates';

const prisma = new PrismaClient();

async function seedMinioBucket() {
  await db.initMinioBucket();
}

async function seedBikaAdmin() {
  await db.helper.createSiteAdmin();
}

const seedMongoIndexes = async () => {
  await db.mongo.migrateIndexes();
};

async function main() {
  console.log('db seed with bun start!');
  try {
    await seedMinioBucket();

    await seedBikaAdmin();

    await seedMongoIndexes();

    await Promise.all([
      seedTemplates(),
      seedLandingPages(),
      seedBlogs(),
      seedHelps(),

      // seedFeatureAbilities()
    ]);

    console.log('db seed finished!');
    await prisma.$disconnect();
    process.exit(0);
  } catch (e) {
    console.error('Error during database seeding:');
    console.error(e);
    await prisma.$disconnect();

    process.exit(1);
  }
}

await main();

// main()
//   .then(async () => {
//     console.log('db seed finished!');
//     await prisma.$disconnect();
//   })
//   .catch(async (e) => {
//     console.error(e);
//     await prisma.$disconnect();
//     process.exit(1);
//   });
