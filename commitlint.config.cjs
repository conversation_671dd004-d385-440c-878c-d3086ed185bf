// =============================================
// Commitlint 配置
// 要求: 1) 允许更长 header 2) 强制以 emoji 开头
// 你可以直接删除或取消注释下面准备好的可选 rules。
// =============================================

// 推荐 emoji 清单 (可按需增删)
const ALLOWED_EMOJIS = [
  '✨', // feat
  '🐛', // fix
  '🔥', // remove / cleanup
  '♻️', // refactor
  '⚡', // perf
  '📝', // docs
  '✅', // test / passing tests
  '🚀', // deploy / improve
  '🎨', // style / ui polish
  '🔧', // chore / config
  '📦', // build / deps
  '🔒', // security
  '🌐', // i18n / l10n
  '📈', // analytics / metrics
  '🧪', // experimental
  '🚧', // wip (慎用，可当作暂存)
];

// 自定义 parser：允许 (可选) emoji + 空格 + type(scope?): subject
// 捕获组: 1=emoji 2=type 3=scope 4=subject
const parserPreset = {
  parserOpts: {
    // headerPattern: 可选 单个 emoji (含可选 VS16) + 空格? + type + (scope)? + : + 空格 + subject
    // 覆盖常用 emoji 范围: Dingbats(2700-27BF) + U+1F300-1F6FF + U+1F900-1F9FF
    // 注意: 原范围从 \u2700 开始，未包含 ♻ (U+267B)。将起始点放宽到 \u2600 以覆盖 ♻ 等符号。
    headerPattern:
      /^([\u2600-\u27BF\u{1F300}-\u{1F6FF}\u{1F900}-\u{1F9FF}]\uFE0F?)?\s?([a-zA-Z]+)(?:\(([^)]+)\))?!?:\s(.+)$/u,
    headerCorrespondence: ['emoji', 'type', 'scope', 'subject'],
  },
};

// 自定义 rule: header 必须包含允许的 emoji 作为第一个“可见”字符
function headerHasEmoji(parsed) {
  const { header } = parsed;
  if (!header) return [false, 'header 缺失'];
  const found = ALLOWED_EMOJIS.find((e) => header.startsWith(e));
  if (found) return [true];
  return [false, `提交必须以允许的 emoji 开头 (例如: ${ALLOWED_EMOJIS.slice(0, 6).join(' ')} ...)`];
}

module.exports = {
  extends: ['@commitlint/config-conventional'], // 继承基础 conventional 规则
  parserPreset, // 覆盖解析: 支持前置 emoji
  plugins: [
    {
      rules: {
        'header-has-emoji': (parsed) => headerHasEmoji(parsed),
      },
    },
  ],
  rules: {
    // ===== 强制要求 =====
    'header-has-emoji': [2, 'always'], // 必须以允许的 emoji 开头
    'header-max-length': [0], // 2, 'always', 120], // 放宽 header 长度

    // 若你希望 scope 必填 / 可空 二选一：
    // 'scope-empty': [2, 'never'], // 现在：要求必须有 scope
    'scope-empty': [0], // 当前改为允许空 scope；按需改回上面

    // 限制 subject 不以这些大小写形式 (保留之前逻辑) — 允许中文/emoji 后常见场景: 直接关闭也可
    'subject-case': [0], // 若想恢复原校验: [2, 'never', ['start-case','pascal-case','upper-case']]

    // ===== 你可按需启用的模板 (默认注释) =====
    // ---- 类型相关 ----
    // 'type-enum': [2, 'always', ['feat','fix','perf','refactor','docs','test','build','chore','ci','revert','style']],
    // 'type-case': [2, 'always', 'lower-case'],
    // 'type-empty': [2, 'never'],

    // ---- Scope 相关 ----
    // 'scope-enum': [2, 'always', ['web','server','mobile','ai','auth','ui']],
    // 'scope-case': [2, 'always', 'kebab-case'],
    // 'scope-max-length': [2, 'always', 20],

    // ---- Subject / Header ----
    // 'subject-empty': [2, 'never'],
    // 'subject-full-stop': [2, 'never', '.'], // 末尾不允许句号
    // 'header-min-length': [2, 'always', 12],

    // ---- Body / Footer ----
    // 'body-leading-blank': [2, 'always'],
    // 'footer-leading-blank': [2, 'always'],
    // 'body-max-line-length': [0], // 0 表示关闭该限制
    // 'footer-max-line-length': [0],

    // ---- 引用 / Issue ----
    // 'references-empty': [2, 'never'], // 强制必须引用 Issue (存在 refs)

    // ---- 其它风格 / 限制示例 ----
    // 'signed-off-by': [2, 'always', 'Signed-off-by:'], // 需配合自定义 rule 或插件
    // 'trailer-exists': [2, 'always', 'Co-authored-by'],
    // 'no-rebase-merge': [2, 'always'], // 自定义: 禁止 rebase merge commit (需插件)

    // ---- 自定义 WIP 限制 (示例，可改 enable) ----
    // 'subject-no-wip': [2, 'always'], // 需自定义 rule: 禁止 subject 以 WIP 开头
  },
};
