import { z } from 'zod';
import { ShareScope } from '../../../../../../packages/bika-server-orm/prisma/prisma-client';
import { ShareScopeSchema } from '../node/bo';
import { AIModelSelectBOSchema } from './bo-ai-model-select';

export const AIUsageSchema = z.object({
  // Our usage
  model: z.string().or(AIModelSelectBOSchema).optional(),
  costCredit: z.number(),

  // Language Model usage in SDK
  inputTokens: z.number().optional(),
  outputTokens: z.number().optional(),
  totalTokens: z.number().optional(),
  reasoningTokens: z.number().nullish(),
  cachedInputTokens: z.number().optional(),
});

export type AIUsage = z.infer<typeof AIUsageSchema>;

export const AIChatShareScopeSchema = z.enum([
  ShareScopeSchema.enum.PUBLISH, // 公开展示  show in bika.ai website
  ShareScopeSchema.enum.DEFAULT, // 默认, only space member can visible
  ShareScopeSchema.enum.PUBLIC_READ, // 公开, internet user can visible
]);

export type AIChatShareScope = z.infer<typeof AIChatShareScopeSchema>;

export const AIChatShareSchema = z.object({
  id: z.string(),
  scope: AIChatShareScopeSchema,
});

export type AIChatShare = z.infer<typeof AIChatShareSchema>;
