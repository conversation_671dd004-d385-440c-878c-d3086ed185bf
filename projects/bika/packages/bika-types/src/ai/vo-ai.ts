import { iStringSchema } from 'basenext/i18n';
import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { SkillsetSelectDTOSchema } from '../skill/dto';
import { UserVOSchema } from '../user/vo-user';
import { AIChatShareSchema } from './bo-ai';
import type { AIMessageBO } from './bo-ai-message';
import { AIIntentParamsSchema } from './bo-intent-params';
import { AIChatContextVOSchema } from './vo-ai-chat-context';
import { AIChatOptionSchema } from './vo-ai-sdk';

/**
 * Intent Resolution Status
 */
export const IntentResolutionStatuses = [
  // No attempt to resolve yet, no communication between parties
  // Sometimes the client receives this message, then automatically initiates chat
  // In certain situations, it feels natural, AI might send several messages showing thought process
  'NOT_STARTED',

  // Dialog has started! One party has initiated conversation
  // Sometimes the bot starts, sometimes the user starts, depends on the situation
  'DIALOG',

  // Needs further clarification from user
  // Usually when parameters are not completely filled
  'NEEDS_DISAMBIGUATION',

  // Needs user confirmation
  // Confirm what? Confirm if the parameters are correct
  // Usually returns UI operation
  // Typically when parameters are just filled and final confirmation is needed
  'NEEDS_CONFIRMATION',

  // Intent is clear, proceed to "complete" (directly enter next intent resolver or execute AI)
  'SUCCESS',
] as const;

export const IntentResolutionStatusSchema = z.enum(IntentResolutionStatuses);

export type IntentResolutionStatus = z.infer<typeof IntentResolutionStatusSchema>;

// Currently AIMessageVO = AIMessageBO
// export const AIMessageVOSchema = AIMessageBOSchema;

// .extend({
// The message's creator is different from the wizard's creator
// creator: UserVOSchema.optional(),
// });

export type AIMessageVO = AIMessageBO; // z.infer<typeof AIMessageVOSchema>;

export const AIChatVOSchema = z.object({
  id: z.string(),
  title: iStringSchema,
  resolutionStatus: IntentResolutionStatusSchema,
  messages: z.array(z.any()),
  skillsets: z.array(SkillsetSelectDTOSchema).optional(),
  intent: AIIntentParamsSchema,
  options: z.array(AIChatOptionSchema).optional(),
  share: AIChatShareSchema.optional(),
});
/**
 * Dialog VO
 */
export type AIChatVO = z.infer<typeof AIChatVOSchema>;

export const AIChatSimpleVOSchema = AIChatVOSchema.pick({
  id: true,
  resolutionStatus: true,
  share: true,
}).extend({
  title: z.string(),
  description: z.string().optional(),
  createdAt: z.string(),
});
export type AIChatSimpleVO = z.infer<typeof AIChatSimpleVOSchema>;

export const AIChatPaginationVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(AIChatSimpleVOSchema),
});
export type AIChatPaginationVO = z.infer<typeof AIChatPaginationVOSchema>;

export const AIResolveType = z.enum([
  // 'UI',
  'MESSAGE',
  'TOOL',
]);
// export const AIResolveUIVOSchema = z.object({
//   type: z.literal(AIResolveType.enum.UI),
//   uiResolve: AIIntentUIResolveDTOSchema,
//   message: z.string().optional(),
// });
// export type AIResolveUIVO = z.infer<typeof AIResolveUIVOSchema>;
export const AIResolveMessageVOSchema = z.object({
  type: z.literal(AIResolveType.enum.MESSAGE),
  // message: z.string(), // @deprecated, use lastMessage instead
  // user option
  option: z.string().optional(),
  contexts: z.array(AIChatContextVOSchema).optional(),

  message: z.custom<AIMessageBO>((v) => v),
});
export type AIResolveMessageVO = z.infer<typeof AIResolveMessageVOSchema> & {
  lastMessage: AIMessageBO;
};

// export const AIResolveToolDTOSchema = z.object({
//   type: z.literal(AIResolveType.enum.TOOL),
//   toolInvocation: AIMessagePartToolInvocationSchema,
//   // 这是前端tool执行过程的message,直接将整个记录传回去，将最后一条message更新，根据messageId
//   // message: AIMessageBOSchema, // z.any(), // 对应 AI SDK 的 Core Message (不是 AI SDK 的 UIMessage 和 Message！！！ https://ai-sdk.dev/docs/reference/ai-sdk-core/core-message)
//   // message: z.string(),
//   // uiMessage: AIMessageBOSchema, // z.any(), // 对应 AI SDK 的 Core Message (不是 AI SDK 的 UIMessage 和 Message！！！ https://ai-sdk.dev/docs/reference/ai-sdk-core/core-message)
//   option: z.string().optional(),
//   // message: z.string(),
//   // toolCallId: z.string(),
// });
// export type AIResolveToolDTO = z.infer<typeof AIResolveMessageVOSchema>;

export const AIResolveVOSchema = z.discriminatedUnion('type', [
  // AIResolveUIVOSchema,
  AIResolveMessageVOSchema,
  // AIResolveToolDTOSchema,
]);

export type AIResolveVO = z.infer<typeof AIResolveVOSchema>;

// Client response: AIResolveVO -> AIResolveUIVO -> AIIntentUIResolveAIO -> XXXAIIntentUIResolveAIO
//                            -> AIResolveMessageVO
