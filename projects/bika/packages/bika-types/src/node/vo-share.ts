import { BasePaginationInfoSchema } from 'basenext/pagination';
import { z } from 'zod';
import { AIChatSimpleVOSchema } from '../ai/vo-ai';
import { TalkExpertKeySchema } from '../space/bo-talk';
import { SpaceVOSchema } from '../space/vo-space';
import { ShareBOSchema } from './bo-share';
import { NodeDetailVOSchema, NodeTreeVOSchema } from './vo-node';

export const ShareVOSchema = ShareBOSchema.extend({
  shortUrlId: z.string().optional(),
  id: z.string(),
});

export type ShareVO = z.infer<typeof ShareVOSchema>;

export const ShareResourceVOSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('NODE'),
    node: NodeDetailVOSchema,
    space: SpaceVOSchema.pick({ id: true, name: true, logo: true }),
  }),
  z.object({
    type: z.literal('AI_CHAT'),
    agent: z.discriminatedUnion('type', [
      z.object({
        type: z.literal('node'),
        node: NodeDetailVOSchema,
      }),
      z.object({
        type: z.literal('expert'),
        expertKey: TalkExpertKeySchema,
      }),
    ]),
    space: SpaceVOSchema.pick({ id: true, name: true, logo: true }),
  }),
]);

export type ShareResourceVO = z.infer<typeof ShareResourceVOSchema>;

export const ListShareResourceVOSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('NODE'),
    resources: z.array(NodeTreeVOSchema),
  }),
  z.object({
    type: z.literal('AI_CHAT'),
    resources: z.array(AIChatSimpleVOSchema),
  }),
]);

export type ListShareResourceVO = z.infer<typeof ListShareResourceVOSchema>;

export const ListShareVOSchema = BasePaginationInfoSchema.extend({
  data: z.array(ListShareResourceVOSchema),
});

export type ListShareVO = z.infer<typeof ListShareVOSchema>;
