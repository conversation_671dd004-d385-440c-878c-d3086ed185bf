import { z } from 'zod';

export const ShareSettingsSchema = z.object({
  autoReplay: z.boolean().optional(),
  // 是否发布到社区
  toCommunity: z.boolean().optional(),
});
export type ShareSettings = z.infer<typeof ShareSettingsSchema>;

export const ShareResourceTypeSchema = z.enum(['NODE', 'AI_CHAT', 'DATABASE_RECORD']);

export const ShareScopeSchema = z.enum([
  'DEFAULT',
  'PUBLIC_READ',
  'PUBLIC_READ_WRITE',
  'ANONYMOUS_READ_WRITE',
  'PUBLISH',
]);

export const ShareBOSchema = z.object({
  resourceType: ShareResourceTypeSchema,
  resourceId: z.string(),
  scope: ShareScopeSchema,
  settings: ShareSettingsSchema.optional(),
  password: z.string().optional(),
});
export type ShareBO = z.infer<typeof ShareBOSchema>;

export const ShortURLRelationTypeSchema = z.enum(['NODE_RESOURCE', 'AI_CHAT']);
