{"name": "@toolsdk.ai/example", "version": "2.0.0-beta.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome format . --write && biome check ."}, "dependencies": {"@bika/contents": "workspace:*", "@bika/ui": "workspace:*", "@toolsdk.ai/sdk-ts": "workspace:*", "ai": "^5", "next": "15.3.3", "react": "18.3.1", "react-dom": "18.3.1"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}}