import { createUIMessageStream } from 'ai';
import { generateNanoID } from 'basenext/utils';
import { describe, expect, it } from 'vitest';
import { MockContext } from '../../__tests__/mock/mock.context';
import { AIController } from '../apis/ai-controller';
import { AIChatSO } from '../server/ai-chat/ai-chat-so';
import { DataStreamUtils } from '../server/ai-chat/data-stream-utils';

describe('Get wizard history', () => {
  it('should get wizard list correctly-page 2', async () => {
    const { user } = await MockContext.initUserContext();
    const nodeId = generateNanoID();

    const mockWizard = async () => {
      const wizardSO = await AIChatSO.newChatByUser(user.id, {
        type: 'COPILOT',
        copilot: {
          type: 'node',
          nodeId,
        },
      });
      await wizardSO.doSaveHumanMessage(
        [],

        {
          id: 'test',
          role: 'user',
          parts: [
            {
              type: 'text',
              text: 'Hello',
            },
          ],
        },
        // {
        //   type: 'MESSAGE',
        //   message: 'Hello',
        // },
        user.id,
      );
      const uiMsgStream = DataStreamUtils.toUIMessageStream({
        id: 'for_test',
        role: 'assistant',
        parts: [
          {
            type: 'text',
            text: 'World',
          },
        ],
        // text: 'World',
      });
      const { message: newMessage } = await wizardSO.doSaveAiResponse(
        uiMsgStream,
        {
          usage: async () => ({
            model: 'default',
            inputTokens: 100,
            outputTokens: 100,
            totalTokens: 1000,
            costCredit: 88,
          }),
          status: 'DIALOG',
          intentParam: {
            type: 'COPILOT',
            copilot: {
              type: 'node',
              nodeId: '123',
            },
          },
        },

        wizardSO.intent,
        user.id,
        // [
        //   {
        //     type: 'HUMAN',
        //     text: 'Hello',
        //     createdBy: user.id,
        //   },
        //   {
        //     type: 'AI',
        //     text: 'World',
        //   },
        //   {
        //     type: 'HUMAN',
        //     text: 'test',
        //     createdBy: user.id,
        //   },
        // ],
      );
      console.log('message mock ', newMessage);
      expect(newMessage).toBeDefined();
      expect(newMessage.parts.length).toBe(1);
    };

    // 创建5个对话
    await Promise.all(Array.from({ length: 5 }, mockWizard));
    const chats = await AIChatSO.page(user, {
      pageNo: 2,
      pageSize: 2,
      type: 'AI_COPILOT',
      nodeId,
    });
    expect(chats.pagination.total).toBe(5);
    expect(chats.data.length).toBe(2);
    const chat = chats.data[0];
    expect(chat.id).toBeDefined();
    expect(chat.title).toBe('Hello');
    expect(chat.description).toBe('World');
    expect(chat.createdAt).toBeDefined();
  });
});

describe('Delete wizard', () => {
  it('should delete wizard correctly', async () => {
    const { user } = await MockContext.initUserContext();
    const nodeId = generateNanoID();
    const mockWizard = async () => {
      const wizardSO = await AIChatSO.newChatByUser(user.id, {
        type: 'COPILOT',
        copilot: {
          type: 'node',
          nodeId,
        },
      });
      await wizardSO.doSaveAiResponse(
        createUIMessageStream({
          originalMessages: [
            {
              id: 'for_test',
              role: 'assistant',
              parts: [
                {
                  type: 'text',
                  text: 'I am a copilot',
                },
              ],
              // text: 'I am a copilot',
            },
          ],
          execute: ({ writer }) => {
            writer.write({
              type: 'start',
              messageId: 'for_test',
            });
            writer.write({
              type: 'finish',
            });
          },
        }),
        {
          usage: async () => ({
            model: 'default',
            inputTokens: 100,
            outputTokens: 100,
            totalTokens: 1000,
            costCredit: 88,
          }),
          // message:
          status: 'DIALOG',
          intentParam: {
            type: 'COPILOT',
            copilot: {
              type: 'node',
              nodeId: '123',
            },
          },
        },
        wizardSO.intent,
        // [
        //   {
        //     type: 'HUMAN',
        //     text: 'Hello',
        //     createdBy: user.id,
        //   },
        //   {
        //     type: 'AI',
        //     text: 'World',
        //   },
        //   {
        //     type: 'HUMAN',
        //     text: 'test',
        //     createdBy: user.id,
        //   },
        // ],
        user.id,
      );
      return wizardSO;
    };
    const wizard = await mockWizard();
    const chats = await AIChatSO.page(user, {
      pageNo: 1,
      pageSize: 5,
      type: 'AI_COPILOT',
      nodeId,
    });
    expect(chats.data.length).toBe(1);

    await AIController.deleteWizard(user, wizard.model.id);
    const chats2 = await AIChatSO.page(user, {
      pageNo: 1,
      pageSize: 5,
      type: 'AI_COPILOT',
      nodeId,
    });
    expect(chats2.data.length).toBe(0);
  });
});
