import { useLocale } from '@bika/contents/i18n';
import { NodeVOAICopilotMenu } from '@bika/domains/editor/client/node-vo-menu/node-vo-ai-copilot-menu';
import { NodeVOMenu } from '@bika/domains/editor/client/node-vo-menu/node-vo-menu';
import { NodeHeaderTitle } from '@bika/domains/node/client/header/node-header-title-component';
import type { AiPageSingleHTMLData } from '@bika/types/ai/bo';
import type { AiPageVO } from '@bika/types/ai/vo';
import type { NodeDetailVO } from '@bika/types/node/vo';
import { useShareContext, useSpaceContext, useSpaceContextForce } from '@bika/types/space/context';
import { IconButton } from '@bika/ui/button-component';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import { Tooltip } from '@bika/ui/tooltip';
import { HeaderPageComponent } from '@bika/ui/web-layout';
import { useTheme } from '@mui/material/styles';
import React, { useEffect } from 'react';

const EmptyImage = () => {
  const theme = useTheme();
  const { t } = useLocale();
  const emptyImage = theme.palette.mode.includes('dark')
    ? '/assets/placeholders/email-invitation-dark.png'
    : '/assets/placeholders/email-invitation-light.png';
  return (
    <div className="flex flex-col items-center justify-center h-full space-y-6">
      <img src={emptyImage} alt="" width={160} height={160} />
      <div className="text-b2 text-[--text-secondary]">{t.resource.ai_page.welcome}</div>
    </div>
  );
};

interface Props {
  value: NodeDetailVO;
}
// const BUTTON_STYLE = 'bg-[--bg-controls] border p-2 border-[--border-default] rounded cursor-pointer';

function AIPageSingleHTMLRenderer(props: { data: AiPageSingleHTMLData }) {
  const content = props.data.content || '';

  return (
    <>
      {!content ? (
        <EmptyImage />
      ) : (
        <iframe srcDoc={content} width="100%" height="100%" title="AI Page HTML Content" />
      )}
    </>
  );
}
export function AIPageVORenderer(props: Props) {
  const { name, permission, id, resource } = props.value;
  const spaceContext = useSpaceContext();
  const { sharing } = useShareContext();
  const data = (resource as AiPageVO)?.data;
  // const searchParams = useSearchParams();
  // const router = useRouter();
  const spaceForce = useSpaceContextForce();
  // 使用自定义的本地存储状态管理方式，避免react-use的useLocalStorage同步问题
  // const storageKey = `ai-page-${id}`;
  // const [chatId, setChatId] = useState<null | string | number>(null);

  // const [isCopilot, setIsCopilot] = useState(false);

  useEffect(() => {
    if (data) return;
    if (sharing) return;
    spaceForce.showAICopilot({
      type: 'node',
      nodeId: id,
    });
    // We intentionally do not include spaceForce.showAICopilot reference separately
    // as spaceForce is stable from context; including it is fine though.
  }, [data, sharing, id, spaceForce]);

  // useEffect(() => {
  //   if (content) return;
  //   spaceForce.showAICopilot({
  //     type: 'node',
  //     nodeId: id,
  //   });
  // }, []);

  // useEffect(() => {
  //   // 打开 Copilot，如果已经有过对话记录，则默认打开最近一次的记录，否则新建一个对话
  //   const storedValue = localStorage.getItem(storageKey);
  //   if (storedValue) {
  //     setChatId(storedValue);
  //     return;
  //   }
  //   setChatId(null);
  // }, [isCopilot]);

  // const createNewChat = () => {
  //   // setChatId((pre) => {
  //     if (typeof pre === 'string') {
  //       return null;
  //     }
  //     return Math.random();
  //   });
  // };

  // useEffect(() => {
  //   // 从设置中跳转到 Copilot
  //   const modifyHTMLParam = searchParams.get('ModifyHTML');
  //   if (modifyHTMLParam !== null) {
  //     if (isCopilot) {
  //       createNewChat();
  //     } else {
  //       setIsCopilot(true);
  //     }
  //     // 清除 URL 参数
  //     const newSearchParams = new URLSearchParams(searchParams.toString());
  //     newSearchParams.delete('ModifyHTML');
  //     router.replace(`${window.location.pathname}?${newSearchParams.toString()}`, { scroll: false });
  //   }
  // }, [searchParams, router]);

  // useEffect(() => {
  //   // 点击对话历史中的一条记录，打开历史数据
  //   const wizardId = searchParams.get('wizardId');
  //   if (wizardId) {
  //     setChatId(wizardId);
  //     const newSearchParams = new URLSearchParams(searchParams.toString());
  //     newSearchParams.delete('wizardId');
  //     router.replace(`${window.location.pathname}?${newSearchParams.toString()}`, { scroll: false });
  //   }
  // }, [searchParams]);

  // const cacheChatId = (id: string) => {
  //   localStorage.setItem(storageKey, id);
  // };

  // const PageIcon = ResourceIconMap.PAGE;

  const { t } = useLocale();

  return (
    <div className="flex h-full">
      <div className="flex-1">
        <HeaderPageComponent
          header={
            <NodeHeaderTitle
              nodeType="PAGE"
              nodeId={id}
              icon={{
                kind: 'node-resource',
                customIcon: props.value?.icon ?? undefined,
                nodeType: 'PAGE',
              }}
              // TODO i18n
              name={name || 'no title'}
              description={props.value?.description ?? ''}
              permission={permission?.privilege}
              button={
                <div className="flex flex-row gap-4">
                  {!sharing && (
                    <>
                      <Tooltip title={t.buttons.edit}>
                        <IconButton
                          color={'neutral'}
                          variant={'plain'}
                          onClick={() => {
                            spaceContext?.showUIDrawer({
                              type: 'resource-editor',
                              props: {
                                screenType: 'NODE_RESOURCE',
                                resourceType: 'PAGE',
                                nodeId: id,
                              },
                            });
                          }}
                        >
                          <EditOutlined color="var(--text-secondary)" />
                        </IconButton>
                      </Tooltip>
                      <NodeVOAICopilotMenu value={props.value} />
                    </>
                  )}
                  <NodeVOMenu value={props.value} detail={props.value} />
                </div>
              }
            />
          }
          // button={
          //   <Stack direction="row" alignItems="center" spacing={1}>
          //     <TopRightButtons
          //       before={
          //         <>
          //           <NodeVOAICopilotMenu value={value} />
          //           <NodeVOMenu value={value} detail={value} />
          //         </>
          //       }
          //     />
          //   </Stack>
          // }
        >
          {!data && <EmptyImage />}
          {data?.kind === 'SINGLE_HTML' && <AIPageSingleHTMLRenderer data={data} />}
        </HeaderPageComponent>
      </div>
    </div>
  );
}
