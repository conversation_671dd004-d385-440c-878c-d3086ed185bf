'use client';

import { useChat } from '@ai-sdk/react';
import { AISkillsetClientRegistry } from '@bika/domains/ai-skillset/client-registry';
import type { AIIntentParams, AIMessageBO } from '@bika/types/ai/bo';
import type { AIChatContextVO } from '@bika/types/ai/vo';
import { AIToolExecutionErrorSchema } from '@bika/types/ai/vo';
import { CONST_PREFIX_AI_MESSAGE } from '@bika/types/database/vo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { useGlobalContext } from '@bika/types/website/context';
import { Box, Stack } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';
import {
  DefaultChatTransport,
  getToolName,
  isToolUIPart,
  lastAssistantMessageIsCompleteWithToolCalls,
  type ToolUIPart,
} from 'ai';
import assert from 'assert';
import type { Locale } from 'basenext/i18n';
import { generateNanoID } from 'basenext/utils';
// import type { Message as AISDKMessage, ToolInvocationUIPart } from '@ai-sdk/ui-utils';
import { motion } from 'framer-motion';
import React, { useCallback, useEffect } from 'react';

import { useAIArtifactGlobalStore } from '../../../website-global-modals/ai-artifact-global-store';
import { Message } from '../wizard/message';

import { AIChatMessage } from './ai-chat-message';
import { AIChatPrompts } from './ai-chat-prompts';
// import { MessageIcon } from './ai-message-icon';
import { AIChatArtifact } from './artifacts/ai-artifact';
import { CreditLimit } from './credit-limit';
import { useAutoResume } from './hooks/use-auto-resume';
import { useChatState } from './hooks/use-chat-state';
import {
  AIChatInput,
  type AIChatInputRefHandle,
  type BaseAIChatInputProps,
} from './input/ai-chat-input';
import { TOOL_RESULT_CANCELED } from './tools/type';
import { useChatScrollToBottom } from './use-chat-scroll-to-bottom';

type Props = BaseAIChatInputProps & {
  // chatId: string;
  api: '/api/ai/admin' | '/api/ai/chat' | '/api/ai/replay';
  // 可以发送 options 给对应接口给予不同行为，该 option 可供用户选择
  // options?: AIChatOption[];
  customBody?: {
    forceLocale?: Locale;
    initAIIntent?: AIIntentParams;
    wizardId?: string;
    messageIndex?: number;
  };
  initialMessages?: AIMessageBO[];
  disabled?: boolean;
  onFinish?: (opts: { message: AIMessageBO }) => void;
  // You can control the artifact whether in RIGHT_SIDE or as UI MODAL
  // 显示模式，窄边 or 独立大窗 or 模态窗
  displayMode?: 'COPILOT' | 'MODAL' | 'VIEW';
  hideInput?: boolean;
  autoResume?: boolean;
  skillsetIcons?: React.ReactNode;
  noScroll?: boolean;
};

export interface AIChatHandle {
  doAppendMessage: (msg: string, contexts?: AIChatContextVO[]) => void;
}

/**
 *
 * Pure AI SDK useChat UI.  DONT DONT DONT use this with trpc!!!
 *
 * @param props
 * @param ref
 * @returns
 */
function InternalAIChatUIRenderer(props: Props, ref: React.Ref<AIChatHandle>) {
  const { inputState, autoResume = true, noScroll } = props;
  const { chatId } = inputState;
  assert(chatId, 'chatId is required');
  const isCopilot = props.displayMode === 'COPILOT';
  const { containerRef, onViewportLeave, onViewportEnter, scrollToBottom, isAtBottom } =
    useChatScrollToBottom();
  const inputRef = React.useRef<AIChatInputRefHandle>(null);

  const {
    setMessages,
    messages,
    error,
    status,
    sendMessage,
    // append,
    // input,
    // setInput,
    // data: chatData,
    // handleInputChange,
    // handleSubmit,
    addToolResult,
    resumeStream,
    stop,
  } = useChat<AIMessageBO>({
    id: chatId,
    transport: new DefaultChatTransport({
      api: props.api,
      prepareSendMessagesRequest: ({ id, messages: prepareMessages, body: requestBody }) => {
        const lastMessage = prepareMessages[prepareMessages.length - 1];
        const option = (requestBody as { option?: string })?.option || inputState.option;

        return {
          body: {
            id,
            lastMessage,
            option,
            contexts: inputState.contexts,
            ...props.customBody,
          },
        };
        // return {
        //   headers: {
        //     'X-Session-ID': id,
        //   },
        //   body: {
        //     messages: messages.slice(-10), // Only send last 10 messages
        //     trigger,
        //     messageId,
        //   },
        // };
      },
    }),
    // transport: {
    //   // prepareSendMessagesRequest: {
    //   //   api: props.api,
    //   // },
    //   //   // api: props.api,
    // },
    messages: props.initialMessages,
    // maxSteps: 5,
    experimental_throttle: 400,
    onFinish: props.onFinish,

    onToolCall: async (opts) => {
      const { toolCall } = opts;
      const toolName = toolCall.toolName;
      const toolCallCargs = toolCall.input;
      const toolCallId = toolCall.toolCallId;
      console.log('TODO Client Tool Call:', toolName, toolCallCargs, toolCallId);
    },

    generateId: () => generateNanoID(CONST_PREFIX_AI_MESSAGE),

    sendAutomaticallyWhen: lastAssistantMessageIsCompleteWithToolCalls,

    // experimental_prepareRequestBody: (body) => {
    //   const lastMessage = body.messages[body.messages.length - 1];
    //   const option = (body.requestBody as { option?: string })?.option || inputState.option;

    //   return {
    //     id: body.id,
    //     lastMessage,
    //     option,
    //     contexts: inputState.contexts,
    //     ...props.customBody,
    //   };
    // },
  });
  console.log('🚀 ~ InternalAIChatUIRenderer ~ messages:', messages);

  useAutoResume({
    autoResume,
    initialMessages: props.initialMessages || [],
    goResume: resumeStream,
    // data: chatData,
    setMessages,
  });

  let parsedCallToolErrors: Record<string, string> | undefined;

  useEffect(() => {
    if (error?.message && typeof error?.message === 'string') {
      console.error('error.message', error?.message);
      try {
        const parsedResult = AIToolExecutionErrorSchema.safeParse(JSON.parse(error?.message));
        if (parsedResult.success) {
          parsedCallToolErrors = parsedResult.data.errors;
          if (
            messages.some((m) =>
              m.parts?.some(
                (t) => isToolUIPart(t) && t.toolCallId === parsedResult.data.toolCallId,
              ),
            )
          ) {
            console.log('🚀 ~ addToolResult ~ :', parsedResult.data.toolCallId);
            addToolResult({
              tool: `tool-${parsedResult.data.name}`,
              toolCallId: parsedResult.data.toolCallId,
              output: {
                isError: true,
                error: {
                  message: parsedResult.data.message,
                },
              },
            });
          }
        }
      } catch (e) {
        console.error('error AIToolExecutionErrorSchema.safeParse', e);
      }
    }
  }, [error?.message, messages, addToolResult]);

  const isCreditError = React.useMemo(() => {
    try {
      const errorObj = JSON.parse(error?.message || '{}');
      return errorObj._error.code === 10002;
    } catch {
      return false;
    }
  }, [error]);

  // const skillsetSelects: SkillsetSelectDTO[] = React.useMemo(() => {
  //   // 打开一个对话历史，对话中调用了 tool，但由于这是历史数据，tool 定义的 execute 方法还没执行，因此 chatData 还是 undefined 状态
  //   // 这会导致这个 tool 对应的 ui 无法渲染
  //   if (!chatData)
  //     return [
  //       { kind: 'preset', key: 'debug' },
  //       { kind: 'preset', key: 'default' },
  //       { kind: 'preset', key: 'bika-ai-page' },
  //       { kind: 'preset', key: 'bika-search' },
  //       { kind: 'preset', key: 'bika-space' },
  //       { kind: 'preset', key: 'bika-media' },
  //       { kind: 'preset', key: 'bika-database' },
  //     ];
  //   return parseSkillsetSelectsFromChatData(chatData);
  // }, [chatData]);

  const globalContext = useGlobalContext();
  // 自动将最新收到的 tool，设置成artifact 吗？
  const [autoRollArtifact, setAutoRollArtifact] = React.useState<boolean>(true);
  const { artifact, setArtifact } = useChatState();

  // 清理 artifact
  useEffect(
    () => () => {
      setArtifact(undefined);
    },
    [],
  );

  const { setData: setGlobalArtifact } = useAIArtifactGlobalStore();

  const lastMessage = React.useMemo(() => {
    if (messages.length === 0) return undefined;

    const newLastMessage = messages[messages.length - 1];
    return newLastMessage;
  }, [messages]);

  const isLastMessageCall = React.useMemo(() => {
    if (!lastMessage || !lastMessage.parts) return false;
    return lastMessage.parts.some((part) => isToolUIPart(part) && part.state === 'input-streaming');
  }, [lastMessage]);

  const lastMsgSkillsetSelects: SkillsetSelectDTO[] = React.useMemo(() => {
    // 打开一个对话历史，对话中调用了 tool，但由于这是历史数据，tool 定义的 execute 方法还没执行，因此 chatData 还是 undefined 状态
    // 这会导致这个 tool 对应的 ui 无法渲染
    if (!lastMessage)
      return [
        { kind: 'preset', key: 'debug' },
        { kind: 'preset', key: 'default' },
        { kind: 'preset', key: 'bika-ai-page' },
        { kind: 'preset', key: 'bika-search' },
        { kind: 'preset', key: 'bika-space' },
        { kind: 'preset', key: 'bika-media' },
        { kind: 'preset', key: 'bika-database' },
      ];
    return lastMessage.metadata?.skillsets || []; // AIMessageAnnotations.parseSkillsets(); // as AIMessageAnnotation[] | []);
    // }, [chatData]);
  }, [lastMessage]);

  // 检查是否有工具需要审批
  const hasToolsPendingApproval = React.useMemo(() => {
    if (!lastMessage || !lastMessage) return false;
    return lastMessage.parts.some((part) => {
      // 只检查处于 call 状态的工具调用
      if (!isToolUIPart(part) || part.state !== 'input-streaming') return false;

      // 检查是否需要审批
      const needApproval = lastMsgSkillsetSelects?.some(
        (skillset) =>
          'needApprovals' in skillset && skillset.needApprovals?.includes(getToolName(part)),
      );

      return needApproval;
    });
  }, [lastMessage, lastMsgSkillsetSelects]);

  const emptyAssistantMessage = useCallback((message: AIMessageBO) => {
    if (message.role !== 'assistant') return false;
    const hasContent = message.parts?.some(
      (part) => (part.type === 'text' && part.text) || isToolUIPart(part),
    );
    return !hasContent;
  }, []);

  // 用于控制 loading 消息的显示，避免在新消息到达前出现空白间隙
  const showLoadingMessage = React.useMemo(() => {
    // 当状态为 submitted 时显示 loading
    if (status === 'submitted') {
      return true;
    }

    // 当状态为 streaming 时，检查是否有助手消息内容
    if (status === 'streaming' && lastMessage) {
      // 如果助手消息还没有内容，继续显示 loading
      return emptyAssistantMessage(lastMessage);
    }

    return false;
  }, [status, lastMessage, emptyAssistantMessage]);

  // 提交消息后，自动滚动到最底部
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (noScroll) return;
    if (status === 'submitted' || status === 'ready' || status === 'error') {
      setTimeout(() => {
        scrollToBottom('smooth');
      }, 100);
    }
  }, [status, noScroll]);

  useEffect(() => {
    if (status === 'streaming') {
      let rafId: number;

      const scroll = () => {
        scrollToBottom('instant');
        rafId = requestAnimationFrame(scroll);
      };

      scroll();

      return () => cancelAnimationFrame(rafId);
    }
    return undefined;
  }, [status, scrollToBottom]);

  // 获取最后一个 dynamic-tool 的部分，判断值被变化，用于自动滚动 artifact 的出现
  const lastTool: ToolUIPart | undefined = React.useMemo(() => {
    if (!lastMessage) return undefined;
    const toolParts = lastMessage.parts.filter((p) => isToolUIPart(p));
    if (toolParts.length === 0) return undefined;
    return toolParts[toolParts.length - 1];
  }, [lastMessage]);

  // Create a stop event handler that intercepts chat stop events
  const handleStop = React.useCallback(() => {
    // If there are tools pending approval, cancel them
    if (hasToolsPendingApproval && lastMessage?.parts) {
      const pendingTools = lastMessage.parts.filter((part) => {
        if (!isToolUIPart(part) || part.state !== 'input-streaming') return false;

        const needApproval = lastMsgSkillsetSelects?.some(
          (skillset) =>
            'needApprovals' in skillset && skillset.needApprovals?.includes(getToolName(part)),
        );

        return needApproval;
      });

      // Cancel all pending approval tools
      for (const toolInvocation of pendingTools) {
        assert(isToolUIPart(toolInvocation), 'Expected toolInvocation to be of type isToolUIPart');
        try {
          console.info('Canceling tool pending approval:', toolInvocation);
          addToolResult({
            tool: `tool-${getToolName(toolInvocation)}`,
            toolCallId: toolInvocation.toolCallId,
            output: TOOL_RESULT_CANCELED,
          });
        } catch (err) {
          console.error('Failed to cancel tool execution:', err);
        }
      }
    }

    // Find the last tool that was being executed (in 'input-streaming' state)
    // Only cancel if there's a tool currently being executed
    if (lastTool?.state === 'input-streaming' && lastTool.toolCallId) {
      try {
        console.info('addToolResult', lastTool);
        // Cancel the last tool execution by adding TOOL_RESULT_CANCELED
        addToolResult({
          tool: `tool-${getToolName(lastTool)}`,
          toolCallId: lastTool.toolCallId,
          output: TOOL_RESULT_CANCELED,
        });
        console.log('Tool execution canceled:', lastTool.toolCallId);
      } catch (err) {
        console.error('Failed to cancel tool execution:', err);
      }
    }
    stop();
  }, [stop, addToolResult, lastTool, hasToolsPendingApproval, lastMessage, lastMsgSkillsetSelects]);

  const messageIndex = props.customBody?.messageIndex;

  // const { api: aiChatHandler } = useAIChatData(selector);

  useEffect(() => {
    if (noScroll) return;
    if (!props.initialMessages?.some((msg) => msg.id === artifact?.message.id)) {
      setArtifact(undefined);
    }
    setTimeout(() => {
      scrollToBottom('smooth');
    }, 500);
  }, [messageIndex, noScroll]);

  useEffect(() => {
    if (messageIndex) {
      setMessages(props.initialMessages ?? []);
    }
  }, [messageIndex, props.initialMessages]);

  // 使用单独的effect来处理artifact更新
  // 当 Last Tool 发生变化，设置该 tool 为 artifact
  useEffect(() => {
    const isCalling = lastTool?.state === 'input-available';
    const isJustUpdated =
      artifact?.tool.state === 'input-available' && lastTool?.state === 'output-available';
    if (
      props.displayMode !== 'COPILOT' &&
      autoRollArtifact === true &&
      lastTool &&
      (isCalling || isJustUpdated) &&
      lastMessage
    ) {
      const skillsetUIMap = AISkillsetClientRegistry.getManySkillsetUI(lastMsgSkillsetSelects);
      const toolUIConfig = skillsetUIMap[getToolName(lastTool)];
      // 必须是强制有 artifact (server-artifact 或者 component)的才会 auto roll
      if (toolUIConfig?.artifact) {
        setArtifact({ message: lastMessage, tool: lastTool, skillsets: lastMsgSkillsetSelects });
      }
    }
  }, [lastTool, autoRollArtifact, lastMessage, props.displayMode, lastMsgSkillsetSelects]);

  const doAppendMessage = React.useCallback(
    (msg: string) => {
      sendMessage(
        {
          role: 'user',
          metadata: {
            contexts: inputState.contexts,
          },

          // _.compact([
          //   inputState.contexts && {
          //     type: 'contexts',

          //   },
          // ]),
          parts: [
            {
              type: 'text',
              text: msg,
            },
          ],
        },
        //     option: inputState.option,
        //     contexts: inputContexts,
        //   },
        // },
      );
      // 确保在 append 调用后再清空
      inputState.patchAIChatInputState({ contexts: [], input: '' });
      inputRef.current?.clearInput();
    },
    [sendMessage, props.customBody, inputState, inputState.contexts],
  );

  React.useImperativeHandle(ref, () => ({
    doAppendMessage,
  }));

  const disabled = React.useMemo(
    () => props.disabled === true || (status !== 'ready' && status !== 'error'),
    [props.disabled, status],
  );

  const isArtifactShow = !isCopilot && !!artifact;

  const initAIIntent = props.customBody?.initAIIntent;

  return (
    <Stack
      sx={{
        flex: 1,
        display: 'flex',
        height: '100%',
      }}
    >
      <Box
        // ref={ref}
        sx={{
          display: 'flex',
          width: '100%',
          alignItems: 'flex-start',
          flexDirection: isArtifactShow ? 'row' : 'column',
          overflowY: isCopilot ? 'auto' : 'hidden',
          flex: 1,
        }}
        ref={containerRef}
      >
        <motion.div
          className="ai-chat-container"
          style={{ display: 'flex', flexDirection: 'column', height: '100%', alignItems: 'center' }}
          animate={{
            width: isArtifactShow ? '50%' : '100%',
            transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] },
          }}
        >
          <Box
            id="ai-chat-headers"
            sx={{
              width: '100%',
            }}
          />
          <Box
            sx={{
              minWidth: '300px',
              display: 'flex',
              width: '100%',
              flexDirection: 'column',
              alignItems: 'center',
              overflowY: isCopilot ? 'none' : 'auto',
              overflowX: 'hidden',
              p: '24px 0 8px 0',
              paddingX: '16px',
              flex: 1,
            }}
            id="ai-chat-messages"
          >
            {messages
              .filter((m) => !emptyAssistantMessage(m))
              .map((m, idx) => (
                <AIChatMessage
                  chatId={chatId}
                  skillsets={m.metadata?.skillsets || []}
                  status={status}
                  key={idx}
                  message={m as AIMessageBO}
                  artifactToolId={artifact?.tool.toolCallId}
                  toolExecuteErrors={parsedCallToolErrors}
                  disabled={disabled || idx !== messages.length - 1} // 除了最后 1 个UI，其它全部disabled，不能交互
                  addToolResult={addToolResult}
                  initAIIntent={initAIIntent}
                  sendUI={async (res) => {
                    console.error('deprecated sendUI', res);
                    // const resolve: AIResolveVO = {
                    //   type: 'UI',
                    //   uiResolve: res,
                    // };
                    // const msg = `/resolve:${JSON.stringify(resolve)}`;
                    // doAppendMessage(msg);
                  }}
                  onSelectTool={(selectToolInvo, selectMsg) => {
                    const skillsets = selectMsg.metadata?.skillsets || [];
                    // if COPILOT, set Global Artifact, and show UI Modal
                    if (props.displayMode === 'COPILOT') {
                      setGlobalArtifact({
                        message: selectMsg,
                        tool: selectToolInvo,
                        skillsets,
                      });
                      globalContext.showUIModal({ name: 'AI_ARTIFACT' });
                    } else {
                      setArtifact({
                        message: selectMsg,
                        tool: selectToolInvo,
                        skillsets,
                      });
                    }

                    // 如果选择了最后一个 tool，激活自动 artifact 弹现
                    if (lastTool === selectToolInvo) {
                      setAutoRollArtifact(true);
                    } else {
                      // 如果选择了不是最后一个 tool，那么就不需要自动滚动了
                      setAutoRollArtifact(false);
                    }
                  }}
                  onCloseTool={() => {
                    setArtifact(undefined);
                    setGlobalArtifact(undefined);
                    setAutoRollArtifact(false);
                  }}
                  sendMessage={async (text) => {
                    doAppendMessage(text);
                  }}
                />
              ))}
            {error && !isCreditError && parsedCallToolErrors == null && (
              <Typography textColor="var(--status-danger)">{error.message}</Typography>
            )}
            {isCreditError && <CreditLimit />}
            {showLoadingMessage && (
              <Stack
                key="loading"
                display="flex"
                flexDirection="row"
                sx={{ width: '100%', maxWidth: '768px' }}
              >
                {/* <MessageIcon type="assistant" initAIIntent={initAIIntent} /> */}
                <Stack
                  sx={{
                    backgroundColor: 'var(--bg-controls)',
                    fontSize: '16px',
                    borderRadius: '8px',
                    py: 2,
                    px: 1,
                    padding: '8px 16px 8px 16px',
                  }}
                >
                  <Message loading />
                </Stack>
              </Stack>
            )}
            <motion.div
              id="last-message-bottom"
              className="shrink-0 min-w-[24px] min-h-[4px]"
              onViewportLeave={onViewportLeave}
              onViewportEnter={onViewportEnter}
            />
          </Box>
          {props.hideInput !== true && (
            <>
              {lastMessage && !isCopilot && (
                <Box px={2}>
                  <AIChatPrompts
                    onClickPrompt={(userPrompt) => {
                      doAppendMessage(userPrompt);
                    }}
                    value={lastMessage.metadata?.prompts}
                  />
                </Box>
              )}
              {!isCopilot && (
                <AIChatInput
                  // handler={aiChatHandler}
                  inputState={inputState}
                  config={props.config}
                  ref={inputRef}
                  status={hasToolsPendingApproval ? 'streaming' : status}
                  stop={handleStop}
                  isAtBottom={isAtBottom || status === 'streaming'}
                  // input={input}
                  // setInput={setInput}
                  disabled={isLastMessageCall || hasToolsPendingApproval || !!props.disabled}
                  skillsetIcons={props.skillsetIcons}
                  handleSubmit={(_e) => {
                    doAppendMessage(inputState.input ?? '');
                    // handleSubmit(e, {
                    //   body: {
                    //     ...props.customBody,
                    //     option: inputState.option,
                    //     contexts: inputState.contexts,
                    //   },
                    //   allowEmptySubmit: true,
                    // });
                    // 使用 setTimeout 确保在 handleSubmit 完成后再清空
                  }}
                />
              )}
            </>
          )}
        </motion.div>

        {/* On Click Tool 的时候， 显示 Artifact, Copilot模式下，不会显示 artifact (auto roll)，除非手工点击 */}
        {artifact && !isCopilot && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{
              width: 'calc(50% - 24px)',
              opacity: 1,
              transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] },
            }}
            exit={{ width: 0, opacity: 0, transition: { duration: 0.2 } }}
            style={{
              flex: 'none',
              margin: '16px 12px',
              position: 'relative',
              height: '100%',
            }}
          >
            <AIChatArtifact
              skillsets={artifact.skillsets}
              message={artifact.message}
              tool={artifact.tool}
              onClickClose={() => {
                setArtifact(undefined);
                setGlobalArtifact(undefined);
                setAutoRollArtifact(false);
              }}
              sx={{
                width: '100%',
                flex: 'none',
                position: 'relative',
              }}
            />
          </motion.div>
        )}
      </Box>
      {props.hideInput !== true && (
        <>
          {/* 这是个 Copilot 使用的组件 */}
          {lastMessage && isCopilot && (
            <Box px={2}>
              <AIChatPrompts
                onClickPrompt={(userPrompt) => {
                  doAppendMessage(userPrompt);
                }}
                value={lastMessage.metadata?.prompts}
              />
            </Box>
          )}
          {isCopilot && (
            <div className="flex justify-center items-center w-full">
              <AIChatInput
                inputState={inputState}
                ref={inputRef}
                status={hasToolsPendingApproval ? 'streaming' : status}
                stop={handleStop}
                isAtBottom={isAtBottom || status === 'streaming'}
                config={props.config}
                // {
                //   ...props.config,
                //   // allowContextMenu: props.allowContextMenu,
                //   // context: props.context,
                // }}
                // {
                //   options: props.options,
                //   // context={props.context}
                //   // allowContextMenu={props.allowContextMenu}
                // }}
                // input={input}
                // setInput={setInput}
                disabled={isLastMessageCall || hasToolsPendingApproval || !!props.disabled}
                skillsetIcons={props.skillsetIcons}
                handleSubmit={(_e) => {
                  doAppendMessage(inputState.input ?? '');
                  // console.log('e', e);
                  // console.log('e', e);
                  // handleSubmit(e, {
                  //   body: {
                  //     ...props.customBody,
                  //     option: inputState.option,
                  //     contexts: inputState.contexts,
                  //   },
                  //   allowEmptySubmit: true,
                  // });
                  // 使用 setTimeout 确保在 handleSubmit 完成后再清空
                  setTimeout(() => {
                    inputState.setContexts([]);
                    inputState.setInput('');
                    // clearAttachments();
                  }, 0);
                }}
              />
            </div>
          )}
        </>
      )}
    </Stack>
  );
}
export const AIChatUIRenderer = React.memo(React.forwardRef(InternalAIChatUIRenderer));
