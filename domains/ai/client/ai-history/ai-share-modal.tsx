import { useTR<PERSON>Query } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import type { AIChatShareScope } from '@bika/types/ai/bo';
import { type ShareBO, ShareScopeSchema, type ShareSettings } from '@bika/types/node/bo';
import { useSpaceContextForce, useSpaceId, useSpaceRouter } from '@bika/types/space/context';
import { Switch } from '@bika/ui/forms';
import HomeOutlined from '@bika/ui/icons/components/home_outlined';
import WebOutlined from '@bika/ui/icons/components/web_outlined';
import { useSnackBar } from '@bika/ui/snackbar';
import { NavHeader } from '@bika/ui/web-layout';
import qs from 'qs';
import type React from 'react';
import { useEffect, useState } from 'react';
import { ShareControls } from '../../../node/client/share-controls';
import { useAIHistoryStore } from './store';

const AI_SHARE_PERMISSION_OPTIONS = [
  {
    icon: <HomeOutlined color={'var(--text-secondary)'} />,
    label: 'share.permission.share_permission_default',
    value: ShareScopeSchema.enum.DEFAULT,
  },
  {
    icon: <WebOutlined color={'var(--text-secondary)'} />,
    label: 'share.permission.share_permission_can_view',
    value: ShareScopeSchema.enum.PUBLIC_READ,
  },
];

interface AIShareModalProps {
  onClose?: () => void;
  wizardId: string;
}

export const AIShareModal: React.FC<AIShareModalProps> = ({ onClose, wizardId }) => {
  const { t } = useLocale();
  const [linkUrl, setLinkUrl] = useState('');
  const spaceId = useSpaceId();
  const { toast } = useSnackBar();
  const { useParams } = useSpaceRouter();
  const params = useParams<{ nodeId: string }>();

  const { getWizard, updateWizard } = useAIHistoryStore();

  const wizard = getWizard(wizardId);

  const [shareId, setShareId] = useState<string | undefined>(wizard?.share?.id);

  const nodeId = params.nodeId;

  const spaceContext = useSpaceContextForce();

  const trpcQuery = useTRPCQuery();
  const toggleShortURLMutation = trpcQuery.share.toggleShortURL.useMutation();
  const upsertShareMutation = trpcQuery.share.upsert.useMutation();

  const { data: shareVO, isLoading } = trpcQuery.share.getByResource.useQuery({
    resourceId: wizardId,
    resourceType: 'AI_CHAT',
  });

  const [shortURL, setShortURL] = useState<string | undefined>();

  const [shareBO, setShareBO] = useState<ShareBO>({
    resourceType: 'AI_CHAT',
    resourceId: wizardId,
    scope: 'DEFAULT',
    settings: {
      autoReplay: false,
      toCommunity: false,
    },
  });

  useEffect(() => {
    if (isLoading) {
      return;
    }
    if (shareVO) {
      setShareBO((prev) => ({
        ...prev,
        scope: shareVO.scope,
        settings: shareVO.settings,
      }));
      setShortURL(shareVO.shortUrlId);
      setShareId(shareVO.id);
    }
  }, [isLoading, shareVO]);

  const [isTogglingShortURL, setIsTogglingShortURL] = useState(false);

  const doToggleShortURL = async () => {
    setIsTogglingShortURL(true);
    const res = await toggleShortURLMutation.mutateAsync(
      {
        relationId: wizardId,
        relationType: 'AI_CHAT',
      },
      {
        onSuccess: () => {
          toast(t.user.updated, {
            variant: 'success',
          });
        },
      },
    );
    setShortURL(res?.id);
    setIsTogglingShortURL(false);
  };

  const upsertShare = async (data: ShareBO) => {
    setShareBO(data);
    const share = await upsertShareMutation.mutateAsync(
      {
        spaceId,
        data: data, // 使用传入的 data 而不是 shareBO
      },
      {
        onSuccess: () => {
          toast(t.user.updated, {
            variant: 'success',
          });
        },
      },
    );
    setShareId(share.id);
    const scope =
      share.scope === 'PUBLIC_READ' && share.settings?.toCommunity
        ? 'PUBLISH'
        : (share.scope as AIChatShareScope);
    updateWizard(wizardId, {
      share: { id: share.id, scope },
    });
  };

  const handleExportScope = async (value: string) => {
    const scope = ShareScopeSchema.parse(value);
    await upsertShare({
      ...shareBO,
      scope,
      settings: {
        ...shareBO.settings,
        toCommunity: scope === 'DEFAULT' ? false : shareBO.settings?.toCommunity,
      },
    });
  };

  const handleShareSetting = async (settings: ShareSettings) => {
    await upsertShare({
      ...shareBO,
      settings,
    });
  };

  useEffect(() => {
    const queryObj = { chatId: wizardId };

    let url = '';
    if (shortURL) {
      url = `/u/${shortURL}`;
    } else if (shareBO.scope === 'PUBLIC_READ' && shareId) {
      url = `/share/${shareId}`;
    } else {
      url = `/space/${spaceId}/node/${nodeId}`;
    }

    setLinkUrl(
      `${window.location.origin}${url}${queryObj.chatId ? `?${qs.stringify(queryObj)}` : ''}`,
    );
  }, [shareBO, nodeId, spaceId, wizardId, shortURL, shareId]);

  const permissionOptions = AI_SHARE_PERMISSION_OPTIONS;

  return (
    <div className={'flex flex-col'}>
      <NavHeader onClose={onClose || (() => spaceContext.showUIModal(null))}>
        {t.share.share} {t.share.ai_conversation}
      </NavHeader>

      <div className="flex-1">
        <ShareControls
          linkUrl={linkUrl}
          shareInfo={{
            shortURL: shortURL,
            password: shareBO.password,
            shareScope: shareBO.scope,
          }}
          permissionOptions={permissionOptions}
          onExportScopeChange={handleExportScope}
          onToggleShortURL={async () => {
            await doToggleShortURL();
          }}
          disabledToggleShortURL={isTogglingShortURL}
          onTogglePassword={() => {}}
          showPasswordToggle={shareBO.resourceType !== 'AI_CHAT'}
        />
        <div className="flex space-x-[16px] mt-[12px]">
          <Switch
            checked={!!shareBO.settings?.autoReplay}
            disabled={shareBO.scope === 'DEFAULT'}
            onChange={(e) => {
              handleShareSetting({
                ...shareBO.settings,
                autoReplay: e.target.checked,
              });
            }}
            endDecorator={
              <div className="text-b2 text-[--text-primary]">{t.share.permission.replay_mode}</div>
            }
          />
          {shareBO.scope === 'PUBLIC_READ' && (
            <Switch
              endDecorator={
                <div className="text-b2 text-[--text-primary]">
                  {t.share.permission.publish_to_the_community}
                </div>
              }
              checked={!!shareBO.settings?.toCommunity}
              onChange={(e) => {
                handleShareSetting({
                  ...shareBO.settings,
                  toCommunity: e.target.checked,
                });
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};
