import { AIChatSO, AISO } from '@bika/domains/ai/server';
import { LauncherSO } from '@bika/domains/ai/server/launcher-so';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, publicProcedure, router } from '@bika/server-orm/trpc';
import {
  AIGenerateImagePropsSchema,
  AIImageBOSchema,
  type AIIntentParams,
  AIIntentParamsSchema,
  AIWriterResponseSchema,
  AIWriterSchema,
  LauncherRenderListSchema,
  LauncherTabTypeSchema,
} from '@bika/types/ai/bo';
import { ChatPaginationDTOSchema, PublishedChatPaginationDTOSchema } from '@bika/types/ai/dto';
import { AIChatVOSchema, AIResolveVOSchema, ArtifactVOSchema } from '@bika/types/ai/vo';
import { SkillsetSelectBOSchema } from '@bika/types/skill/bo';
import { SkillsetSearchDTOSchema } from '@bika/types/skill/dto';
import {
  type SkillsetVO,
  SkillsetVOPaginationSchema,
  SkillsetVOSchema,
} from '@bika/types/skill/vo';
import assert from 'assert';
import { z } from 'zod';
import { AIArtifactSO } from '../../ai-artifacts/ai-artifact-so';
import { AISkillsetServerRegistry } from '../../ai-skillset/server-registry';
import { AttachmentSO } from '../../attachment/server/attachment-so';
import { AIController } from './ai-controller';

export const aiRouter = router({
  generateImages: protectedProcedure
    .input(
      AIImageBOSchema.and(
        AIGenerateImagePropsSchema.pick({
          prompt: true,
          size: true,
          n: true,
        }),
      ).and(
        z.object({
          spaceId: z.string(),
        }),
      ),
    )
    .mutation(async (opts) => {
      const userId = opts.ctx.session!.userId;
      const spaceId = opts.input.spaceId;
      const user = await UserSO.init(userId);
      const space = await user.getSpace(spaceId);
      assert(space, `Space with id ${spaceId} not found for user ${userId}`);
      const coinsAccount = await space.billing.getCoinsAccount();
      const attaches = await AttachmentSO.generateImages(opts.input, coinsAccount);

      return attaches.map((attach) => {
        const vo = attach.toVO();
        return vo;
      });
    }),

  listSkills: protectedProcedure
    .output(SkillsetVOPaginationSchema.array())
    .query(async (opts) => []),

  callAIWriter: protectedProcedure
    .input(
      z.object({
        spaceId: z.string().optional(),
        writerDTO: AIWriterSchema,
        userInput: z.string().optional(),
        context: z
          .object({
            createdAt: z
              .string()
              .optional()
              .describe('The time when the http request was created, ISO8601 format'),
          })
          .optional(),
      }),
    )
    .output(AIWriterResponseSchema)
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      // console.log('writerDTO:', input.writerDTO);
      const userId = opts.ctx.session!.userId;
      const user = await UserSO.init(userId);

      const context = {
        locale: ctx.locale,
        createdAt: input.context?.createdAt || new Date().toISOString(),
        userId: ctx.session?.userId,
        spaceId: input.spaceId,
      };

      if ('writerDTO' in input && 'userInput' in input) {
        const result = await AIController.quickWrite(
          input.writerDTO,
          input.userInput || '',
          context,
          user,
        );
        return {
          data: result.data,
          value: result.value,
          success: result.success ?? true, // Ensure success is a boolean
          message: result.message,
        };
      }

      return {
        success: false,
        message: 'Invalid input, writerDTO and userInput are required',
      };
    }),

  /**
   * 抓取launcher commands
   */
  audio2Text: protectedProcedure
    .input(
      z.object({
        path: z.string(),
      }),
    )
    .output(z.string().nullable())
    .mutation(async (opts) => {
      const { input } = opts;
      const userId = opts.ctx.session!.userId;
      const user = await UserSO.init(userId);
      console.log('[audio2Text] input.path', input.path);
      // TODO implement use tempAttachment instead
      const attachment = await AttachmentSO.createByPresignedPut(user, input.path);
      console.log('[audio2Text] attachment', attachment);
      const text: string | null = await AISO.audio2TextByAttachment(attachment);
      return text;
    }),
  /**
   * 抓取launcher commands
   */
  fetchLauncherCommands: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        tab: LauncherTabTypeSchema,
        query: z.string().optional(),
      }),
    )
    .output(LauncherRenderListSchema.array())
    .query(async (opts) => {
      const { input } = opts;
      const userId = opts.ctx.session!.userId;
      const spaceId = input.spaceId;

      return LauncherSO.search(userId, spaceId, input.tab, input.query);
    }),

  fetchChat: publicProcedure
    .input(
      z.object({
        chatId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const chat = await AIChatSO.get(input.chatId);
      if (ctx.session?.userId) {
        // 异步处理未读状态
        chat.markAsRead(ctx.session.userId);
      }
      return chat.toVO();
    }),

  // /**
  //  *
  //  * @deprecated 不再使用这个，用 ai-wizard2 （AI SDK Streaming Protocol)
  //  */
  // resolveMessage: protectedProcedure
  //   .input(
  //     z.object({
  //       wizardId: z.string(),
  //       resolve: AIResolveVOSchema,
  //     }),
  //   )
  //   .mutation(async (opts) => {
  //     const { ctx, input } = opts;
  //     return AIController.resolveMessage(ctx, input.wizardId, input.resolve);
  //   }),

  resolveWizard: protectedProcedure
    .input(
      z.object({
        wizardId: z.string(),
        resolve: AIResolveVOSchema,
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      return AIController.resolveWizard(ctx, input.wizardId, input.resolve);
    }),

  // wizardResolveByUI: protectedProcedure
  //   .input(z.object({
  //     wizardId: z.string(),
  //     uiResolveVO: z.any(),
  //   }))
  //   .mutation(async (opts: any) => {
  //     const { input } = opts;
  //     const uiResolveVO = input.uiResolveVO as AIIntentUIResolveAIO;
  //     const wizardSO = await AIChatSO.get(input.wizardId);
  //     await wizardSO.uiResolve(uiResolveVO);

  //     return wizardSO.toVO();
  //   }),

  // wizardResolveByMessage: protectedProcedure
  //   .input(z.object({
  //     wizardId: z.string(),
  //     message: z.any(),
  //   }))
  //   .mutation(async (opts) => {
  //     const { input } = opts;
  //     const wizard = await AIChatSO.get(input.wizardId);
  //     await wizard.humanSay(input.message);
  //     return wizard.toVO();
  //   }),

  newWizard: protectedProcedure
    .input(
      z.object({
        // 如果不传space ID，以member身份运行
        spaceId: z.string().optional(),
        intent: AIIntentParamsSchema,
      }),
    )
    .output(AIChatVOSchema)
    .mutation(async (opts) => {
      const { input } = opts;
      const userId = opts.ctx.session!.userId;
      const user = await UserSO.init(userId);
      let wizardSO: AIChatSO;
      if (input.spaceId) {
        const exist = await user.existSpace(input.spaceId);
        if (exist) {
          const memberSO = await user.getMember(input.spaceId);
          wizardSO = await AIChatSO.newChatByMember(memberSO!.id, input.intent as AIIntentParams);
        } else {
          wizardSO = await AIChatSO.newChatByUser(userId, input.intent as AIIntentParams);
        }
      } else {
        wizardSO = await AIChatSO.newChatByUser(userId, input.intent as AIIntentParams);
      }
      if (
        input.intent.type === 'COPILOT' &&
        input.intent.copilot?.type === 'node' &&
        input.intent.copilot?.nodeId
      ) {
        EventSO.node.onUpdate(await NodeSO.init(input.intent.copilot.nodeId));
      }
      return wizardSO.toVO();
    }),

  chatPage: protectedProcedure.input(ChatPaginationDTOSchema).query(async (opts) => {
    const userId = opts.ctx.session!.userId;
    const user = await UserSO.init(userId);
    const { input } = opts;
    return AIChatSO.page(user, input);
  }),

  publishedChats: publicProcedure.input(PublishedChatPaginationDTOSchema).query(async (opts) => {
    const { input } = opts;
    // get from es
    return AIChatSO.paginatePublishedAIChats(input);
  }),

  deleteWizard: protectedProcedure
    .input(
      z.object({
        wizardId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input } = opts;
      const userId = opts.ctx.session!.userId;
      const user = await UserSO.init(userId);
      return AIController.deleteWizard(user, input.wizardId);
    }),

  executeTool: protectedProcedure
    .input(
      z.object({
        toolCallId: z.string(),
        chatId: z.string().optional(), // 可选，避免 toolCallId 重复，找错
      }),
    )
    .mutation(async (opts) => {
      const { input } = opts;
      const userId = opts.ctx.session!.userId;
      const user = await UserSO.init(userId);

      const toolResult = await AIChatSO.executeTool(
        { toolCallId: input.toolCallId, chatId: input.chatId },
        {
          user,
        },
      );
      return toolResult;
    }),

  fetchArtifact: publicProcedure
    .input(
      z.object({
        artifactId: z.string(),
      }),
    )
    .output(ArtifactVOSchema)
    .query(async (opts) => {
      const { input } = opts;
      const artifact = await AIArtifactSO.getById(input.artifactId);
      return artifact.toVO();
    }),

  searchSkillset: protectedProcedure
    .input(SkillsetSearchDTOSchema.optional())
    .output(SkillsetVOPaginationSchema)
    .query(async (opts) => {
      const { pageNo, pageSize, ...params } = opts.input || {};
      const userId = opts.ctx.session!.userId;
      const user = await UserSO.init(userId);
      return AISkillsetServerRegistry.search(user, {
        ...params,
        pagination: { pageNo, pageSize },
        locale: user.locale,
      });
    }),

  getSkillset: protectedProcedure
    .input(SkillsetSelectBOSchema)
    .output(SkillsetVOSchema.optional())
    .query(async (opts) => {
      const { input } = opts;
      const userId = opts.ctx.session!.userId;
      const user = await UserSO.init(userId);
      return AISkillsetServerRegistry.getSkillsetVO(user, input, user.locale);
    }),

  getSkillsets: protectedProcedure
    .input(z.array(SkillsetSelectBOSchema))
    .output(z.array(SkillsetVOSchema)) // .optional())
    .query(async (opts) => {
      const { input } = opts;
      const userId = opts.ctx.session!.userId;
      const user = await UserSO.init(userId);
      // input 是 数组，改写
      const vos: SkillsetVO[] = [];
      for (const select of input) {
        const vo = await AISkillsetServerRegistry.getSkillsetVO(user, select, user.locale);
        if (vo) {
          vos.push(vo);
        }
      }
      return vos;
    }),
});
