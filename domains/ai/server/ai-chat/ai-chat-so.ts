import assert from 'node:assert';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { createToolCallRequestContext } from '@bika/domains/ai-skillset/utils';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { TalkSO } from '@bika/domains/talk/server/talk-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { type AIChatModel, type AIMessageModel, type AIToolModel, db } from '@bika/server-orm';
import {
  type AIChatShare,
  AIChatShareScopeSchema,
  type AIChatSource,
  type AIIntentParams,
  type AIMessageBO,
  type AIUsage,
} from '@bika/types/ai/bo';
import type { ChatPaginationDTO, PublishedChatPaginationDTO } from '@bika/types/ai/dto';
import type {
  AIChatPaginationVO,
  AIChatSimpleVO,
  AIChatVO,
  AIMessageVO,
  AIResolveVO,
  IntentResolutionStatus,
} from '@bika/types/ai/vo';
import { CONST_PREFIX_AI_MESSAGE, CONST_PREFIX_WIZ } from '@bika/types/database/vo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import type { ApiFetchRequestContext } from '@bika/types/user/vo';
import {
  getToolName,
  isToolUIPart,
  lastAssistantMessageIsCompleteWithToolCalls,
  readUIMessageStream,
  streamText,
  type ToolUIPart,
  type UIMessage,
  type UIMessageChunk,
  type UIMessageStreamWriter,
} from 'ai';
import { iStringParse, type Locale } from 'basenext/i18n';
import { generateNanoID } from 'basenext/utils';
import { AIArtifactSO } from '../../../ai-artifacts/ai-artifact-so';
import { AISkillsetServerRegistry } from '../../../ai-skillset/server-registry';
import type { SkillsetHandlerContext } from '../../../ai-skillset/types';
import { AIModelPicker } from '../ai-model-picker';
import { AISearchSO } from '../ai-search-so';
import { AISO } from '../ai-so';
import { AICreditManager } from '../credit-manager';
import type { AIModelOptions, IStreamChatPrompt, IStreamChatReturn } from '../types';
import {
  enhanceConvertToModelMessages,
  getArtifactIdsFromToolInvocation,
  type ResolutionResultInfo,
} from '../types';
import { DataStreamUtils } from './data-stream-utils';
import { IntentSO } from './intent-so';

export type IResolveResult = { message: AIMessageVO; intent: IntentSO };

export class AIChatSO {
  private _model: AIChatModel;

  private _intent: IntentSO;

  private _user: UserSO;

  private constructor(model: AIChatModel, intent: IntentSO, user: UserSO) {
    this._model = model;
    this._intent = intent;
    this._user = user;
  }

  public async getMessages(): Promise<AIMessageBO[]> {
    const messagesPOs = await this.getMessagesPOs();
    const userIds: Set<string> = new Set(
      messagesPOs
        .map((i) => (i.message?.role === 'user' ? i.createdBy : undefined))
        .filter((i) => i) as string[],
    );
    const users = await UserSO.buildMapByIds(Array.from(userIds));
    const chatHistories: AIMessageBO[] = messagesPOs
      .filter((i) => i.message?.parts?.length)
      .map((po) => {
        const message = po.message as AIMessageBO;
        if (message.role === 'user' && po.createdBy) {
          message.metadata = {
            ...message.metadata,
            creator: users[po.createdBy]?.toVO(),
          };
        }
        return message;
      })
      .filter((i) => i);
    return chatHistories;
  }

  public async getSpace(): Promise<SpaceSO | undefined> {
    const spaceId = await this._intent.getSpaceId();
    if (!spaceId) {
      return undefined;
    }
    return SpaceSO.init(spaceId);
  }

  /**
   * Chat ID
   */
  public get id() {
    return this._model.id;
  }

  public get createdAt() {
    return this._model.createdAt;
  }

  public get published(): boolean {
    return this._model.share?.scope === AIChatShareScopeSchema.enum.PUBLISH;
  }

  static async newChatByUser(userId: string, intent: AIIntentParams) {
    return AIChatSO.create({
      roleType: 'USER',
      roleId: userId,
      intent,
    });
  }

  static async newChatByMember(unitMemberId: string, intent: AIIntentParams) {
    return AIChatSO.create({
      roleType: 'MEMBER',
      roleId: unitMemberId,
      intent,
    });
  }

  /**
   * 给指定成员发送消息。
   */
  static async sendMessageToMember(params: {
    member: MemberSO;
    intent: AIIntentParams;
    message: string;
    source?: AIChatSource;
    read?: boolean;
  }) {
    const { member, message, ...body } = params;
    const chat = await AIChatSO.create({
      roleType: 'MEMBER',
      roleId: member.id,
      ...body,
    });

    let aiMessage: AIMessageVO | undefined;

    const user = await member.getUser();
    const ctx = createToolCallRequestContext(user, '');
    const result = await chat.resolve(
      ctx,
      {
        type: 'MESSAGE',
        message: {
          id: generateNanoID('ai-msg'),
          role: 'user',
          parts: [{ type: 'text', text: message }],
        },
      },
      'en',
      // dataStream,
    );

    for await (const uiMessage of readUIMessageStream<AIMessageBO>({
      stream: result.resolution.result,
    })) {
      console.log('Current message state:', uiMessage);
      aiMessage = uiMessage;
    }
    return aiMessage;
  }

  async getMessagesPOs(): Promise<AIMessageModel[]> {
    const aiMessagesPOs = await db.mongo
      .aiMessage(this.id)
      .find({
        chatId: this._model.id,
      })
      .sort({ createdAt: 1 }); // 1 表示升序，-1 表示降序;
    return aiMessagesPOs;
  }

  public get user() {
    return this._user;
  }

  public get model() {
    return this._model;
  }

  public get intent(): IntentSO {
    return this._intent;
  }

  public get intentResolutionStatus() {
    return this._intent.resolutionStatus;
  }

  static async init(id: string) {
    const wizardPO = await db.mongo.aiChat.findOne({
      id,
    });
    if (!wizardPO) {
      throw new ServerError(errors.ai.chat_not_found);
    }
    const user = await AIChatSO.initializeUser(wizardPO);
    const intent = await IntentSO.create(
      wizardPO,
      wizardPO.intent,
      wizardPO.intentResolutionStatus as IntentResolutionStatus,
      user,
    );
    return new AIChatSO(wizardPO, intent, user);
  }

  // 获取chat的user
  // private async getUser(): Promise<UserSO> {
  //   let user;
  //   switch (this._model.roleType) {
  //     case 'MEMBER':
  //       {
  //         const member = await this.getHumanMember();
  //         user = member?.getUser();
  //       }
  //       break;
  //     case 'USER':
  //       user = await UserSO.init(this._model.roleId);
  //       break;
  //     default:
  //       throw new Error(`Unknown AIWizard roleType: ${this._model.roleType}`);
  //   }
  //   return user;
  // }

  public async resolve(
    ctx: ApiFetchRequestContext,
    resolveObj: AIResolveVO,
    // lastMessage: AIMessageBO,
    forceLocale?: Locale,
    dataStreamWriter?: UIMessageStreamWriter,
  ): Promise<{ resolution: ResolutionResultInfo; intent: IntentSO }> {
    //  先保存人类消息, 防止服务端还未执行完，用户刷新网页之后 消息丢失, 会更新chatHistories最后一条消息
    const lastMessage: AIMessageBO = resolveObj.message;
    // if (resolveObj.type === 'MESSAGE') {
    //   lastMessage = resolveObj.message;
    // }
    // else if (resolveObj.type === 'UI') {
    //   // ensure the message not null (debt)
    //   console.warn(`[WARN]resolveObj.message is undefined`);
    //   // resolveObj.message = `/resolve:${JSON.stringify(resolveObj.uiResolve)}`;
    //   lastMessage = {
    //     id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
    //     role: 'user',
    //     parts: [{ type: 'text', text: `/resolve:${JSON.stringify(resolveObj.uiResolve)}` }],
    //   };
    // }
    if (this.isEnd()) {
      throw new Error(`Dialog已经结束，不允许再聊天了: ${this._model.id}`);
    }
    const user = ctx.session?.userId ? await UserSO.init(ctx.session.userId) : await this._user;

    const { locale } = ctx;
    // 这里对内存中的对话，添加了聊天记录
    const messagesPOs = await this.getMessagesPOs();
    const chatHistories: AIMessageBO[] = messagesPOs.map((po) => po.message as AIMessageBO);
    await this.doSaveHumanMessage(chatHistories, lastMessage, user.id);

    const creditManager = AICreditManager.init(
      {
        userId: user.id,
        spaceId: await this._intent.getSpaceId(),
        accountType: this.roleType === 'MEMBER' ? 'SPACE' : 'USER',
      },
      'CHAT',
    );

    await creditManager.checkCredit();

    // if (resolveObj.type === 'TOOL') {
    //   // 如果是 tool resolve， 替换最后一条消息（含了 tool-result 执行结果)，在 tool 里加入 invocation
    //   // const lastMessage = chatMessages[chatMessages.length - 1];
    //   chatMessages[chatMessages.length - 1] = resolveObj.message;
    //   // console.log('Tool Resolve', resolveObj.uiMessage);
    // }
    const { resolution, intent } = await this._intent.resolveYield(
      ctx,
      resolveObj,
      forceLocale || locale,
      chatHistories,
      user,
    );

    const [mainStream, dataStream] = resolution.result.tee();
    if (dataStreamWriter) {
      // ui consume
      dataStreamWriter.merge(mainStream);
    } else {
      resolution.result = mainStream;
    }

    // backend consume
    const message = await this.consumeDataStream(dataStream, resolution, intent, user, lastMessage);
    await creditManager.costAICredit(`${this.id}:${message.id}`);

    return {
      resolution,
      intent,
    };
  }

  private async consumeDataStream(
    uiMessageStream: ReadableStream<UIMessageChunk>,
    resolution: Omit<ResolutionResultInfo, 'result'>,
    intent: IntentSO,
    user: UserSO,
    lastMessage?: AIMessageBO,
  ) {
    const r = await this.doSaveAiResponse(
      uiMessageStream,
      resolution,

      intent,
      user.id,
      lastMessage,
    );
    return r.message;
  }

  async getHumanMember(): Promise<MemberSO> {
    return MemberSO.init(this._model.roleId);
  }

  /**
   * 当intent已经完成了，就是结束Wizard Dialog，不让继续聊天了
   *
   * @returns
   */
  isEnd(): boolean {
    return this.intentResolutionStatus === 'SUCCESS';
  }

  private static async initializeUser(wizardPO: {
    roleType: 'MEMBER' | 'USER';
    roleId: string;
  }): Promise<UserSO> {
    let user: UserSO | undefined;
    switch (wizardPO.roleType) {
      case 'MEMBER':
        {
          // const member = await this.getHumanMember();
          const member = await MemberSO.init(wizardPO.roleId);
          user = await member?.getUser();
        }
        break;
      case 'USER':
        user = await UserSO.init(wizardPO.roleId);
        break;
      default:
        throw new Error(`Unknown AIWizard roleType: ${wizardPO.roleType}`);
    }
    return user;
  }

  static async get(wizardId: string) {
    const wizardPO = await db.mongo.aiChat.findOne({
      id: wizardId,
    });

    if (!wizardPO) {
      throw new ServerError(errors.ai.chat_not_found);
    }

    const user = await AIChatSO.initializeUser(wizardPO);

    const intent = await IntentSO.create(
      wizardPO,
      wizardPO.intent,
      wizardPO.intentResolutionStatus as IntentResolutionStatus,
      user,
    );

    return new AIChatSO(wizardPO, intent, user);
  }

  static async create(params: {
    roleType: 'MEMBER' | 'USER';
    roleId: string;
    intent: AIIntentParams;
    source?: AIChatSource;
    read?: boolean;
  }) {
    const { roleType, roleId, intent, source, read } = params;
    const initResolutionStatus = 'DIALOG';
    const user = await AIChatSO.initializeUser({ roleType, roleId });
    const newChatModel: AIChatModel = {
      id: generateNanoID(CONST_PREFIX_WIZ),
      intent,
      intentResolutionStatus: initResolutionStatus, // 这里上面AI产生了对话，默认就是对话过，客户端收到后，就不会主动发起对话
      roleType,
      roleId,
      metadata: source ? { source } : undefined,
      read,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: user.id,
      updatedBy: user.id,
    };

    const intentSO = await IntentSO.create(newChatModel, intent, initResolutionStatus, user);
    const prologue = await intentSO.getPrologue();

    // 开场白也算一条message
    if (prologue) {
      const initMsg: AIMessageBO = {
        id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
        role: 'assistant',
        ...prologue,
      };
      // {
      //   id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
      //   role: 'assistant',
      //   content: '',
      //   ...prologue,
      //   // parts: prologue.parts,
      //   // voice: prologue.voice,
      //   // ui: prologue.ui,
      //   // prompts: prologue.prompts,
      // };

      // newWizardModel.messages.push(initMsg);

      const newMessagePOs: AIMessageModel[] = [];
      newMessagePOs.push({
        id: initMsg.id, // generateNanoID(CONST_PREFIX_AI_MESSAGE),
        chatId: newChatModel.id,
        message: initMsg,
        usages: undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: user.id,
        updatedBy: user.id,
      });

      await db.mongo.aiMessage(newChatModel.id).insertMany(newMessagePOs);
    }

    const newWizardPO = await db.mongo.aiChat.create(newChatModel);

    return new AIChatSO(newWizardPO, intentSO, user);
  }

  async delete() {
    await db.mongo.aiChat.deleteOne({
      id: this._model.id,
    });
  }

  async toDetailVO() {
    return {
      messages: await this.getMessages(),
      intent: this._model.intent,
    };
  }

  public async getLastHumanMessage() {
    const messages = (await this.getMessages()) as AIMessageBO[];
    for (let i = messages.length - 1; i >= 0; i -= 1) {
      // 倒序查找
      if (messages[i].role === 'user') {
        return messages[i];
      }
    }
    return null;
  }

  public async getLastAIMessage() {
    const messages = await this.getMessages();
    for (let i = messages.length - 1; i >= 0; i -= 1) {
      // 倒序查找
      if (messages[i].role === 'assistant') {
        return messages[i];
      }
    }
    return null;
  }

  /**
   * Only the last messages
   *
   * @returns
   */
  async toVO(): Promise<AIChatVO> {
    const wizardVO: AIChatVO = {
      id: this._model.id,
      title: this._intent.title,
      intent: this._intent.model,
      messages: await this.getMessages(),
      resolutionStatus: this.intentResolutionStatus,
      options: await this._intent.getOptions(),
      share: this._model.share,
    };
    return wizardVO;
  }

  async toSimpleVO(): Promise<AIChatSimpleVO> {
    const messages = await this.getMessages();
    const title = messages
      .find((m) => m.role === 'user')
      ?.parts?.findLast((p) => p.type === 'text')?.text;

    const description = messages
      .find((m, index) => m.role === 'assistant' && index > 0)
      ?.parts?.findLast((p) => p.type === 'text')?.text;

    return {
      id: this._model.id,
      createdAt: new Date(this._model.createdAt).toISOString(),
      title: iStringParse(title),
      description: iStringParse(description),
      resolutionStatus: this.intentResolutionStatus,
      share: this._model.share,
    };
  }

  // public newMessage(message: string, type: AIMessageType = AIMessageType.TEXT): AIMessageSO {
  // }

  /**
   * 语音输入！传入附件ID
   * @param attachmentId
   */
  // public async humanSpeak(attachmentId: string) {
  //   // TODO: 语音解释成文字并做处理
  //   return this.humanSay(`语音附件${attachmentId}`);
  // }

  /**
   * 以UI的方式按了按钮
   * @param arg0
   */
  // async onResolveUI(resolveObj: AIResolveVO): Promise<{ message: AIMessageBO, intent: IntentSO }> {
  //   const resolveUI = AIResolveUIVOSchema.parse(resolveObj);
  //   if (this.isEnd()) {
  //     throw new Error(`Dialog已经结束，不允许再聊天了: ${this._model.id}`);
  //   }

  //   const { intent: intentSO, resolution } = await this._intent.resolve(resolveUI);
  //   this._intent = intentSO; // This IntentSO maybe be a new intent
  //   return this.doSaveAiResponse(resolution, intentSO);
  // }
  async doSaveHumanMessage(chatHistories: AIMessageBO[], lastMessage: AIMessageBO, userId: string) {
    if (lastMessage.role === 'user') {
      chatHistories.push(lastMessage);
      await this.doSaveHumanNewMessages([lastMessage], userId);
      return;
    }
    if (lastAssistantMessageIsCompleteWithToolCalls({ messages: [lastMessage] as UIMessage[] })) {
      console.log('tool result change, setting tool result');
      // 替换最后一条消息
      chatHistories.splice(-1, 1, lastMessage);
      await db.mongo.aiMessage(this.id).updateOne(
        {
          id: lastMessage.id,
          chatId: this.id,
        },
        {
          $set: {
            message: lastMessage,
          },
        },
      );
      return;
    }
    throw new ServerError(errors.ai.message_have_been_updated);
  }

  private async doSaveHumanNewMessages(messages: AIMessageBO[], userId: string) {
    const humanMessagePOs: AIMessageModel[] = messages.map((message) => ({
      id: message.id, // generateNanoID(CONST_PREFIX_AI_MESSAGE),
      chatId: this._model.id,
      message,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: userId,
      updatedBy: userId,
    }));
    await db.mongo.aiMessage(this.id).insertMany(humanMessagePOs);
  }

  /**
   * DB Upsert Tool Invocation
   *
   * @param msgId
   * @param toolInvocation
   * @returns
   */
  private async upsertToolInvocation(
    msgId: string,
    resolution: Omit<ResolutionResultInfo, 'result'>,
    toolInvocation: ToolUIPart,
    skillsets: SkillsetSelectDTO[] | undefined = undefined,
  ) {
    let aiToolPO = await db.mongo.aiTool.findOneAndUpdate(
      { toolCallId: toolInvocation.toolCallId },
      {
        chatId: this._model.id, // chatId 用于索引，避免 toolCallId 重复
        messageId: msgId,
        state: toolInvocation.state,
        toolCallId: toolInvocation.toolCallId,
        toolName: getToolName(toolInvocation),
        data: toolInvocation,
        prompt: resolution.prompt,
        options: resolution.options,
        skillsets,
      },
    );
    if (!aiToolPO) {
      aiToolPO = await db.mongo.aiTool.create({
        id: generateNanoID('ait'),
        chatId: this._model.id,
        messageId: msgId,
        toolCallId: toolInvocation.toolCallId,
        toolName: getToolName(toolInvocation),
        state: toolInvocation.state,
        data: toolInvocation,
        prompt: resolution.prompt,
        options: resolution.options,
        skillsets,
      });
    }
    assert(
      aiToolPO,
      `AI Tool Invocation not created: ${msgId} ${JSON.stringify(toolInvocation.toolCallId, null, 2)}`,
    );
    return aiToolPO;
  }

  public static async executeTool(
    index: {
      toolCallId: string;
      chatId?: string; // 可选，避免 toolCallId 重复，找错
    },
    // toolName: string,
    // skillsetsSelects: SkillsetSelectDTO[],
    context: SkillsetHandlerContext,
  ) {
    const { toolCallId } = index;
    const indexFinder = index.chatId ? { toolCallId, chatId: index.chatId } : { toolCallId };

    const aiToolPO: AIToolModel | null = await db.mongo.aiTool.findOne(indexFinder);
    assert(aiToolPO, `Tool Model not found: ${JSON.stringify(indexFinder)}`);

    const aiToolInvocation = aiToolPO.data as ToolUIPart;
    assert(
      aiToolInvocation.state === 'input-available',
      `Tool Invocation is not in call state: ${toolCallId}`,
    );
    const toolName = aiToolPO.toolName;

    const skillsetsSelects = (aiToolPO.skillsets || []) as SkillsetSelectDTO[];

    const toolset = await AISkillsetServerRegistry.parseAISDKToolsets(
      skillsetsSelects,
      context,
      true,
    );
    const tool = toolset[toolName!];
    if (!tool) {
      console.error(`Tool ${toolName} not found in skillsets`, skillsetsSelects);
      return null;
    }
    if (!tool.execute) {
      console.error(`Tool ${toolName} has no execute function`, skillsetsSelects);
      return null;
    }
    const result = tool.execute(aiToolInvocation.input, { toolCallId, messages: [] });
    return result;
  }

  /**
   *  持久化
   *
   * @param humanSay
   * @param aiResolution
   * @param intentSO
   * @param userId
   * @returns
   */
  async doSaveAiResponse(
    uiMessageStream: ReadableStream<UIMessageChunk>,
    resolution: Omit<ResolutionResultInfo, 'result'>,
    intentSO: IntentSO,
    userId: string,
    lastMessage?: AIMessageBO,
  ) {
    const aiResMsg = await DataStreamUtils.parseUIMessage(
      uiMessageStream,
      lastMessage?.role === 'assistant' ? lastMessage : undefined,
    );
    assert(aiResMsg);

    const skillsets = aiResMsg.metadata?.skillsets;

    // Tool Invocation 处理，保存
    for (const part of aiResMsg.parts || []) {
      if (isToolUIPart(part)) {
        // 记录 tool invocation 的调用 ID，对应的数据库
        if (part.state === 'input-streaming' || part.state === 'input-available') {
          console.log(
            '新的 tool call',
            part.toolCallId,
            getToolName(part),
            part.state,
            part.input,
            skillsets,
          );
        } else if (part.state === 'output-available') {
          // 取数据库寻找对应的 tool call id 的tool invocation，标记 result （有可能不存在，因为有些 tool 是服务端运行直接到结果到 message 里）
          console.log(
            '新的 tool result',
            part.toolCallId,
            getToolName(part),
            part.state,
            part.input,
            part.output,
            skillsets,
          );
        } else {
          console.error(
            'tool invocation error',
            part.state,
            part.toolCallId,
            getToolName(part),
            part.input,
            part.output,
            part.errorText,
          );

          // console.log('TODO: 新的 tool invocation 状态', part.state);
        }
        await this.upsertToolInvocation(aiResMsg.id, resolution, part, skillsets);
      }
    }

    const newAISay: AIMessageBO = aiResMsg;

    const intentParams = intentSO.toVO();

    const wizardPO = await db.mongo.aiChat.updateOne(
      {
        id: this._model.id,
      },

      {
        $set: {
          intent: intentParams,
          intentResolutionStatus: intentSO.resolutionStatus,
          updatedBy: userId,
        },
      },
    );
    assert(wizardPO.matchedCount);

    // collect artifact usages
    const artifactUsages = await this.getAIArtifactUsages(newAISay);
    const usages = [...(resolution.usage ? [await resolution.usage()] : []), ...artifactUsages];
    const aiMessagePO = {
      id: newAISay.id,
      chatId: this._model.id,
      message: newAISay,
      usages,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: userId,
      updatedBy: userId,
    };
    let updatedPO = await db.mongo
      .aiMessage(this.id)
      .findOneAndUpdate({ id: newAISay.id, chatId: this.id }, aiMessagePO);
    if (!updatedPO) {
      updatedPO = await db.mongo.aiMessage(this.id).insertOne(aiMessagePO);
    }
    assert(updatedPO, `[doSaveAiResponse] AI Message not updated or created : ${newAISay.id}`);

    return {
      message: newAISay,
      intent: intentSO,
    };
  }

  public get roleType() {
    return this._model.roleType;
  }

  public get roleId() {
    return this._model.roleId;
  }

  async getAIArtifactUsages(message: AIMessageBO): Promise<AIUsage[]> {
    const artifactIds = getArtifactIdsFromToolInvocation(message);
    const usages = await AIArtifactSO.getUsagesByIds(artifactIds);
    return usages;
  }

  /**
   * Human Message Resolve，区别是，UIResolve直接调用UI，而Message Resolve是需要组装上下文聊天历史的
   *
   * @param resolveObj
   * @returns
   */
  public async message(
    ctx: ApiFetchRequestContext,
    message: string,
  ): Promise<{ message: AIMessageBO; intent: IntentSO }> {
    const resolveObj: AIResolveVO = {
      type: 'MESSAGE',
      message: {
        id: generateNanoID(CONST_PREFIX_AI_MESSAGE),
        role: 'user',
        parts: [{ type: 'text', text: message }],
      },
    };
    const r = await this.resolve(ctx, resolveObj);
    const lastMessage = await this.getLastAIMessage();

    if (!lastMessage) {
      throw new Error('Cannot get the last ai assistant message');
    }
    return {
      message: lastMessage,
      intent: r.intent,
    };
  }

  async markAsRead(userId: string) {
    if (this._model.read !== false) {
      return;
    }
    this._model.read = true;
    await db.mongo.aiChat.updateOne({ id: this.id }, { $set: { read: true, updatedBy: userId } });

    const { type } = this.intent.model;
    if (type !== 'AI_NODE') {
      return;
    }
    const [user, node] = await Promise.all([
      UserSO.init(userId),
      NodeSO.init(this.intent.model.nodeId),
    ]);
    // 激活 Talk，内含发送 SSE 事件通知
    await TalkSO.upsertByNode(node, user);
  }

  static async getUnreadChatCount(nodeId: string): Promise<number> {
    const count = await db.mongo.aiChat.countDocuments({
      'intent.nodeId': nodeId,
      read: false,
    });
    return count;
  }

  // public async ui(ctx: ApiFetchRequestContext, uiResolve: AIIntentUIResolveDTO) {
  //   const resolveObj: AIResolveVO = {
  //     type: 'UI',
  //     uiResolve,
  //   };
  //   return this.resolve(ctx, resolveObj);
  // }

  static async getWizardIdByNodeId(nodeId: string): Promise<string | undefined> {
    // according intent.nodeId, find the wizard.id
    const wizard = await db.mongo.aiChat.findOne(
      {
        'intent.nodeId': nodeId,
      },
      {
        _id: 0,
        id: 1,
      },
      {
        sort: {
          createdAt: -1,
        },
      },
    );
    return wizard?.id;
  }

  static async getPublishedChatIds(): Promise<string[]> {
    const chatIds = await db.mongo.aiChat.find(
      {
        'share.scope': AIChatShareScopeSchema.enum.PUBLISH,
      },
      {
        _id: 0,
        id: 1,
      },
      {
        sort: {
          updatedAt: -1,
        },
      },
    );
    return chatIds.map(({ id }) => id);
  }

  private static parseChatPaginationQuery(param: ChatPaginationDTO, user?: UserSO) {
    const { type, shareScope, read } = param;

    let query = {};

    switch (type) {
      case 'AI_COPILOT':
        query = {
          'intent.type': 'COPILOT',
          'intent.copilot.nodeId': param.nodeId,
        };
        break;
      case 'AI_NODE':
        query = {
          'intent.type': 'AI_NODE',
          'intent.nodeId': param.nodeId,
        };
        break;
      case 'AI_BUILDER':
        query = {
          'intent.type': 'BUILDER',
          'intent.spaceId': param.spaceId,
          createdBy: user?.id,
        };
        break;
      case 'AI_SUPERVISOR':
        query = {
          'intent.type': 'SUPERVISOR',
          'intent.spaceId': param.spaceId,
        };
        break;
      default:
        query = {};
        break;
    }

    if (shareScope) {
      query = {
        ...query,
        'share.scope': shareScope,
      };
    }

    if (read === undefined) {
      return query;
    }
    if (read === false) {
      return { ...query, read };
    }

    // 兼容旧数据，read 不存在也是已读
    return { ...query, $or: [{ read: true }, { read: { $exists: false } }] };
  }

  static async page(user: UserSO, param: ChatPaginationDTO): Promise<AIChatPaginationVO> {
    const { pageNo, pageSize } = param;
    const query = AIChatSO.parseChatPaginationQuery(param, user);
    // query aiChat
    const [rows, total] = await Promise.all([
      db.mongo.aiChat.find(query, undefined, {
        sort: {
          updatedAt: -1, // Sort by updatedAt descending
        },
        skip: (pageNo - 1) * pageSize,
        limit: pageSize,
      }),
      db.mongo.aiChat.countDocuments(query),
    ]);
    // build pagination
    const pagination = { pageNo, pageSize, total };
    if (!rows.length) {
      return { pagination, data: [] };
    }

    // build pagination chat view data
    const data = await Promise.all(
      rows.map((w) => {
        const intent = new IntentSO(
          w,
          w.intent,
          w.intentResolutionStatus as IntentResolutionStatus,
          user,
        );
        const chat = new AIChatSO(w, intent, user);
        return chat.toSimpleVO();
      }),
    );
    return { pagination, data };
  }

  static async paginatePublishedAIChats(
    param: PublishedChatPaginationDTO,
  ): Promise<AIChatPaginationVO> {
    return AISearchSO.searchPublishedAIChats(param);
  }
  /**
   *  调去 AI LLM， streaming 式返回，同时获取AI Message BO和 Usage
   *
   * @param prompt
   * @param options
   * @param dataStreamWriter
   * @param returnPrompts 返回消息体里，用户下一个可以说话的 prompts 建议
   * @returns
   */
  public static async streamChat(
    // 强制使用 messages，确保 chat history 正确被使用
    prompt: IStreamChatPrompt,
    options: AIModelOptions,
    _msgOptions: {
      // 默认 的AISDK，stream，是会卡在 await steps 的
      // 是否直接消耗整个 stream？  默认都自动从后台开始消费，
      // 若 disableAutoStream = true，则消耗则依赖于客户端，客户端 刷新就会被打断，消息无保存，避免消耗更多 tokens
      // 若 autoStream，则浏览器刷新后，直接取回 stream 继续
      disableAutoStream?: boolean;

      // 返回 prompt tips？控制从 DataStream.data 返回
      returnPrompts?: string[];
    },
  ): Promise<IStreamChatReturn> {
    let skillsetTools = prompt.skillsets
      ? await AISkillsetServerRegistry.parseAISDKToolsets(prompt.skillsets, {
          dataStreamWriter: options.dataStreamWriter,
          user: prompt.user,
          space: prompt.space,
        })
      : undefined;

    if (prompt.tools) {
      skillsetTools = skillsetTools
        ? await AISkillsetServerRegistry.mergeToolSet([prompt.tools, skillsetTools])
        : prompt.tools;
    }

    prompt.tools = skillsetTools;

    const messages = prompt.messages ? enhanceConvertToModelMessages(prompt.messages) : undefined;

    const result = await AIModelPicker.withRoutedModel(options, async (model) =>
      streamText({
        model,
        onFinish: async (data) => {
          if (options.onFinish) options.onFinish(data);
        },
        onChunk: (event) => {
          if (options.onChunk) options.onChunk(event);
        },
        onError: (error) => {
          if (options.onError) options.onError(error);
        },
        onStepFinish: (stepResult) => {
          if (options.onStepFinish) options.onStepFinish(stepResult);
        },
        // IStreamChatPrompt 没有单独 prompt 内容，只有 messages 历史
        system: prompt.system,
        messages,
        tools: prompt.tools,
        stopWhen: undefined,
      }),
    );

    return {
      result: result.toUIMessageStream({
        messageMetadata: ({ part }) => {
          if (part.type === 'start') {
            return {
              aiModel: options?.model,
              skillsets: prompt?.skillsets,
              creator: prompt.user?.toVO(),
            };
          }
          return undefined;
        },
        generateMessageId: () => generateNanoID('msg'),
        sendSources: true,
        originalMessages: prompt.messages as UIMessage[],
      }),
      usage: async () => {
        const sdkUsage = await result.totalUsage;
        const usage = await AISO.parseAICreditCost(options.model, sdkUsage);
        return usage;
      },
      // reuse IStreamChatPrompt as prompt type (already provided)
      prompt: prompt,
      options,
    };
  }

  static async setShare(id: string, share: AIChatShare) {
    await db.mongo.aiChat.updateOne({ id }, { $set: { share } });
  }
}
