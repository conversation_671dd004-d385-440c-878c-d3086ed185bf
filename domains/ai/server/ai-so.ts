import assert from 'node:assert';
import * as fs from 'node:fs';
import path from 'node:path';
import { PresetImageModelsServerConfig } from '@bika/contents/config/server/ai/ai-model-config';
import { getAIModelPricesConfig } from '@bika/contents/config/server/pricing/ai/ai-models-price';
import type {
  AIGenerateImageProps,
  AIUsage,
  IAIModelSelectBO,
  PresetLanguageAIModelDef,
} from '@bika/types/ai/bo';
import { OpenAIWhisperAudio } from '@langchain/community/document_loaders/fs/openai_whisper_audio';
import {
  convertToModelMessages,
  experimental_generateImage as generateImage,
  generateText,
  type LanguageModelUsage,
  type ModelMessage,
  type StreamTextResult,
  stepCountIs,
  streamObject,
  streamText,
  type ToolSet,
} from 'ai';
import _ from 'lodash';
import type { IAttachmentSO } from 'sharelib/attachment';
import { AIModelPicker } from './ai-model-picker';
import {
  type AIModelOptions,
  enhanceConvertToModelMessages,
  type IAIStreamYield,
  type IPrompt,
  type IPromptWithSchema,
  type IStreamTextPrompt,
} from './types';

export { tool } from 'ai';

export class AISO {
  static async parseAICreditCost(
    aiModelDef: PresetLanguageAIModelDef | IAIModelSelectBO | undefined,
    aiTokenUsage: LanguageModelUsage,
  ): Promise<AIUsage> {
    let costCredit = 0;

    // 强制弄一个 preset，以作为计算基础，即使是 auto 模型
    let calcModelSelect: IAIModelSelectBO;
    if (!aiModelDef || (typeof aiModelDef === 'object' && aiModelDef.kind === 'auto')) {
      calcModelSelect = { kind: 'preset', model: AIModelPicker.getSystemAIModel() };
    } else if (typeof aiModelDef === 'string') {
      calcModelSelect = { kind: 'preset', model: aiModelDef as PresetLanguageAIModelDef };
    } else if (typeof aiModelDef === 'object' && aiModelDef.kind === 'preset') {
      calcModelSelect = aiModelDef;
    } else {
      assert(aiModelDef.kind === 'custom');
      calcModelSelect = aiModelDef as IAIModelSelectBO;
    }
    assert(
      calcModelSelect && calcModelSelect.kind !== 'auto',
      'AI model select kind must not be auto',
    );

    if (calcModelSelect.kind === 'preset') {
      const modelPriceConfig = getAIModelPricesConfig(calcModelSelect.model);
      const { inputTokens, outputTokens } = aiTokenUsage;
      // filter NAN
      if (!_.isNaN(inputTokens) && !_.isNaN(outputTokens)) {
        // 1000 tokens = 1 credit 小数点五入(不四舍)
        costCredit = Math.ceil(
          (modelPriceConfig.inputCredit / 1000) * (inputTokens || 0) +
            (modelPriceConfig.outputCredit / 1000) * (outputTokens || 0),
        );
      } else {
        return {
          inputTokens: 0,
          outputTokens: 0,
          totalTokens: 0,
          model: calcModelSelect,
          costCredit: 0,
        };
      }
    }
    return {
      ...aiTokenUsage,
      model: calcModelSelect,
      costCredit,
    };
  }

  /**
   * 将附件的音频转换成文字
   *
   * @param attachmentSO
   * @returns
   */
  public static async audio2TextByAttachment(attachmentSO: IAttachmentSO) {
    const blob = await attachmentSO.getObjectBlob();
    const buffer = await blob.arrayBuffer();

    // 创建临存文件到/tmp，用完就删
    const localTmpFilePath = `/tmp/${attachmentSO.path}`;

    // Ensure the directory path
    const directoryPath = path.dirname(localTmpFilePath);
    if (!fs.existsSync(directoryPath)) {
      // If it doesn't exist, create the directory
      fs.mkdirSync(directoryPath, { recursive: true });
    }

    fs.writeFileSync(localTmpFilePath, new Uint8Array(buffer));

    const res = await AISO.audio2Text(localTmpFilePath);

    fs.unlinkSync(localTmpFilePath);
    return res;
  }

  /**
   * Bika内部系统调用AI，不可选择模型和参数，通过环境变量设置好了
   *
   * @param prompt
   * @param options
   */
  public static async systemInvoke(prompt: string): Promise<string> {
    return AISO.invoke(prompt, {
      model: AIModelPicker.getSystemAIModel(),
      temperature: 0,
    });
  }

  public static async invoke(prompt: string, options: AIModelOptions) {
    return AISO.invokeByAISDK(prompt, options);
  }

  /**
   * 基于 AI SDK 的流式调用，支持DeepSeek Reasoning （Danger，不支持 user 和 log记录调用，仅限于测试、Make 命令试用
   *
   * @param prompt
   * @param options
   * @returns
   */
  public static async *dangerStreamYield(
    prompt: Pick<IPrompt, 'prompt'>,
    options: AIModelOptions,
    printConsoleDebug: boolean = true,
  ): AsyncGenerator<IAIStreamYield> {
    const aiSDKStream = await AIModelPicker.withRoutedModel(options, async (model) =>
      streamText({
        prompt: prompt.prompt,
        model,
      }),
    );
    // const aiSDKStream = await this.streamText(prompt, options);

    let isReasoning = false;
    let reasoningContent = '';
    let content = '';

    if (printConsoleDebug)
      process.stdout.write(`${new Date()} - ${options.model} - Start AI streaming......\n`);

    const { fullStream } = aiSDKStream;

    for await (const sPart of fullStream) {
      let chunkStr: string | undefined;
      if (sPart.type === 'reasoning-delta') {
        chunkStr = sPart.text;
        reasoningContent = reasoningContent.concat(chunkStr);
        isReasoning = true;
      } else if (sPart.type === 'text-delta') {
        chunkStr = sPart.text;
        content = content.concat(chunkStr);
        isReasoning = false;
        // else {
        // }
        // } else if (sPart.type === 'step-start' || sPart.type === 'step-finish' || sPart.type === 'finish') {
        // ignore
      } else {
        console.warn('ignore: ', sPart);
        // throw new Error(`Unknown stream part type: ${sPart.type}`);
      }

      if (printConsoleDebug) process.stdout.write(chunkStr || '');

      yield {
        isReasoning,
        chunkContent: chunkStr,
        reasoningContent,
        content,
      };
    }
  }

  /**
   *
   * @param prompt
   * @param debug 是否打印AI过程
   * @returns
   */
  public static async systemInvokeJSON(prompt: string): Promise<unknown> {
    const res = await AISO.invoke(prompt, {
      model: AIModelPicker.getSystemAIModel(),
      json: true,
    });

    const jsonStr = res as string;
    try {
      return JSON.parse(jsonStr);
    } catch (e) {
      console.log('AI invokeJSON parseJson error', e, ' return Str', jsonStr);
      return {};
    }
  }

  public static async invokeByAISDK(userPrompt: string, options: AIModelOptions) {
    // Use centralized routed invocation with health + fallbacks
    return AIModelPicker.withRoutedModel(options, async (model) => {
      const { text } = await generateText({ model, prompt: userPrompt });
      return text;
    });
  }

  /**
   * AI 生图，通常不要直接调用，用 Attachment.generateImages，有 log 和 usage 记录
   *
   * @param props
   * @returns
   */
  public static async generateImages(
    props: AIGenerateImageProps,
    // prompt: string,
    // size: `${number}x${number}` | undefined = undefined,
    // n: number | undefined = undefined,
  ) {
    const { prompt, size, n } = props;
    const imageModelConfig = PresetImageModelsServerConfig[props.imageModel];
    const provider = AIModelPicker.parseCustomAISDKProvider(imageModelConfig);

    // Check if the provider supports image generation
    if (!('image' in provider) || typeof provider.image !== 'function') {
      throw new Error(
        `Provider type '${imageModelConfig.type}' does not support image generation. Please use a provider that supports image generation (e.g., OpenAI, Azure AI).`,
      );
    }

    const result = await generateImage({
      model: provider.image(imageModelConfig.modelId), // imageModelConfig.modelId // openai.image('dall-e-3'),
      prompt,
      size: size as `${number}x${number}` | undefined,
      n,
    });
    const { images } = result;
    console.log('Generated images:', images, props);
    return images;
  }

  public static async imageToText(imageUrl: string): Promise<string> {
    const provider = AIModelPicker.getAIProviderByOptions({ model: 'openai/gpt-4o' });
    // url to base64
    const response = await fetch(imageUrl);
    const buffer = await response.arrayBuffer();
    const base64 = Buffer.from(buffer).toString('base64');
    const mimeType = response.headers.get('content-type') || 'image/png';
    const imageData = `data:${mimeType};base64,${base64}`;
    const { text } = await generateText({
      model: provider('gpt-4o'),
      messages: convertToModelMessages([
        {
          role: 'user',

          parts: [
            {
              type: 'text',
              text: `请提取并返回图片中的所有文本内容，要求：
1. 保持原有的格式和布局
2. 保留所有换行和空格
3. 按照从上到下、从左到右的顺序排列
4. 不要添加任何解释或描述
5. 如果有表格，请使用适当的格式保持表格结构
6. 只返回文本内容，不要返回其他描述`,
            },
            {
              type: 'file',
              mediaType: mimeType,
              url: imageData,
            },
          ],
        },
      ]),
      system:
        'You are an OCR expert. Extract all text from images while preserving the original formatting, layout, and structure exactly as it appears in the image.',
      maxOutputTokens: 1000,
      temperature: 0, // 设置为0以获得更一致的结果
    });
    return text;
  }

  public static async fileToTextStream(
    prompt: { messages: ModelMessage[]; system: string },
    options: AIModelOptions,
  ) {
    const { messages, system } = prompt;
    return AIModelPicker.withRoutedModel(options, async (model) =>
      streamText({
        model,
        system,
        messages,
        onError: (error) => {
          console.error('fileToTextStream error:', error);
        },
      }),
    );
  }

  public static async streamObject(
    prompt: IPromptWithSchema,
    options: Omit<AIModelOptions, 'onChunk' | 'onStepFinish'>,
  ) {
    return AIModelPicker.withRoutedModel(options as AIModelOptions, async (model) =>
      streamObject({
        model,
        schema: prompt.schema,
        output: 'object',
        mode: 'json',
        prompt: prompt.prompt,
        messages: prompt.messages ? convertToModelMessages(prompt.messages) : undefined,
        system: `${prompt.system}\n <result_format> return the result in json mode, and do not add any other text</result_format>`,
      }),
    );
  }

  /**
   * 不带 usage cost 统计、不带 skillsets parsing，如是聊天，请使用 streamChat
   *
   * @param prompt
   * @param options
   * @returns
   */
  public static async streamText(
    prompt: IStreamTextPrompt,
    options: AIModelOptions,
  ): Promise<StreamTextResult<ToolSet, never>> {
    const messages = prompt.messages ? enhanceConvertToModelMessages(prompt.messages) : undefined;
    return AIModelPicker.withRoutedModel(options, async (model) =>
      streamText({
        model,
        onFinish: async (data) => {
          if (options.onFinish) options.onFinish(data);
        },
        onChunk: (event) => {
          if (options.onChunk) {
            options.onChunk(event);
          }
        },
        onError: (error) => {
          if (options.onError) {
            options.onError(error);
          }
        },
        onStepFinish: (stepResult) => {
          if (options.onStepFinish) {
            options.onStepFinish(stepResult);
          }
        },
        prompt: prompt.prompt,
        system: prompt.system,
        messages,
        tools: prompt.tools,
        stopWhen: stepCountIs(prompt.maxSteps || 20),
      }),
    );
  }

  /**
   * 音频到文字
   *
   * @param audioPath
   * @returns
   */
  public static async audio2Text(audioPath: string | Blob): Promise<string | null> {
    const loader = new OpenAIWhisperAudio(audioPath);
    const docs = await loader.load();
    if (docs.length > 0) {
      return docs[0].pageContent;
    }
    return null;
  }
}
