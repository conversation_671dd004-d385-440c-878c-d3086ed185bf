import { LoadingMask } from '@bika/ui/loading-mask';
import { type GeneratedImage, ImagesArtifact } from '../../ai-artifacts/images-artifact';
import type { SkillsetUIMap } from '../types';
import { ImageSkillsetName } from './type';

const ImageToolsetUI: SkillsetUIMap = {
  [ImageSkillsetName.generate_image]: {
    artifact: ({ toolInvocation, skillsets }) => {
      console.log('[debug]', toolInvocation);
      if (toolInvocation?.output == null) {
        return <LoadingMask></LoadingMask>;
      }

      return (
        <ImagesArtifact
          dataList={(toolInvocation?.output ?? []) as GeneratedImage[]}
          skillsets={skillsets}
          content={JSON.stringify(toolInvocation?.output)}
          tool={toolInvocation}
        />
      );
    },
  },
};

export default ImageToolsetUI;
