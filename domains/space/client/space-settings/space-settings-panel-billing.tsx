import { useTRPCQuery } from '@bika/api-caller/context';
import {
  type FeatureFormatterDefinition,
  featuresGroupFormatterConfigs,
} from '@bika/contents/config/server/pricing/features-group';
import { useLocale } from '@bika/contents/i18n/context';
import { PureDashboardRenderer } from '@bika/domains/dashboard/client/pure-dashboard-renderer';
import type { WidgetRenderVO } from '@bika/types/dashboard/vo';
import {
  BillingBooleanPlanFeatureSchema,
  BillingDatePlanFeatureSchema,
  BillingFixNumberPlanFeatureSchema,
  BillingUsagePlanFeatureSchema,
  type SpacePlanType,
} from '@bika/types/pricing/bo';
import type {
  DateFeatureVO,
  EntitlementFeatureVO,
  FixedNumberFeatureVO,
  SupportedFeatureVO,
  UsageFeatureVO,
} from '@bika/types/pricing/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { formatBytes } from '@bika/types/utils';
import { useGlobalContext } from '@bika/types/website/context';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import CloseOutlined from '@bika/ui/icons/components/close_outlined';
import { Modal } from '@bika/ui/modal';
import { Skeleton } from '@bika/ui/skeleton';
import { useSnackBar } from '@bika/ui/snackbar';
import { NavHeader } from '@bika/ui/web-layout';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import timezone from 'dayjs/plugin/timezone';
import { compact } from 'lodash';
import { useMemo } from 'react';
import { useSubscribePlatform } from '../hooks/use-subscribe-platform';
import {
  SpaceBillingStatusComponent,
  type UpgradeButtonProps,
} from './space-billing-status-component';

dayjs.extend(localizedFormat);
dayjs.extend(timezone);

const subscribeProcessColor: Record<SpacePlanType, string> = {
  FREE: '--rainbow-brown2',
  STARTER: '--rainbow-indigo2',
  PLUS: '--rainbow-indigo2',
  PRO: '--rainbow-purple2',
  TEAM: '--rainbow-yellow1',
  BUSINESS: '--rainbow-indigo4',
  COMMUNITY: '--rainbow-brown2',
  ENTERPRISE: '--rainbow-indigo2',
  ENTERPRISE_PRIVATE_CLOUD: '--rainbow-purple2',
  ENTERPRISE_SELF_HOSTED: '--rainbow-yellow1',
};

export const subscribeButtonColor: Record<SpacePlanType, string> = {
  FREE: '--rainbow-brown5',
  STARTER: '--rainbow-indigo5',
  PLUS: '--rainbow-indigo5',
  PRO: '--rainbow-purple5',
  TEAM: '--rainbow-tangerine5',
  BUSINESS: '--rainbow-indigo5',
  COMMUNITY: '--rainbow-brown5',
  ENTERPRISE: '--rainbow-indigo5',
  ENTERPRISE_PRIVATE_CLOUD: '--rainbow-purple5',
  ENTERPRISE_SELF_HOSTED: '--rainbow-tangerine5',
};

// 新增升级按钮文字颜色映射
const upgradeButtonTextColor: Record<SpacePlanType, string> = {
  FREE: '--rainbow-brown5',
  STARTER: '--rainbow-indigo5',
  PLUS: '--rainbow-indigo5',
  PRO: '--rainbow-purple5',
  TEAM: '--rainbow-tangerine5',
  BUSINESS: '--rainbow-indigo5',
  COMMUNITY: '--rainbow-brown5',
  ENTERPRISE: '--rainbow-indigo5',
  ENTERPRISE_PRIVATE_CLOUD: '--rainbow-purple5',
  ENTERPRISE_SELF_HOSTED: '--rainbow-tangerine5',
};

const isUsageFeature = (entitlement: EntitlementFeatureVO): entitlement is UsageFeatureVO =>
  BillingUsagePlanFeatureSchema.options.some((feature) => feature === entitlement.feature);

const isFixedNumberFeature = (
  entitlement: EntitlementFeatureVO,
): entitlement is FixedNumberFeatureVO =>
  BillingFixNumberPlanFeatureSchema.options.some((feature) => feature === entitlement.feature);

const isSupportedFeature = (entitlement: EntitlementFeatureVO): entitlement is SupportedFeatureVO =>
  BillingBooleanPlanFeatureSchema.options.some((feature) => feature === entitlement.feature);

const isDateFeature = (entitlement: EntitlementFeatureVO): entitlement is DateFeatureVO =>
  BillingDatePlanFeatureSchema.options.some((feature) => feature === entitlement.feature);

export function SpaceSettingsPanelBilling() {
  const spaceContext = useSpaceContextForce();
  const { timezone: tz, appEnv } = useGlobalContext();
  const spaceId = spaceContext.data.id;
  const { toast } = useSnackBar();
  // 当前空间站的订阅信息
  const subscription = spaceContext.data?.subscription;
  const locale = useLocale();
  const { t } = locale;
  const trpcQuery = useTRPCQuery();
  // const { data: usages, refetch: refetchUsage, isLoading } = trpcQuery.billing.usage.useQuery({ spaceId });
  const { data: entitlements, isLoading } = trpcQuery.billing.entitlements.useQuery({ spaceId });
  const { mutate: cancelSubscription } = trpcQuery.billing.cancelSubscription.useMutation();
  const { mutate: getCustomerPortal, isPending: loadingPortal } =
    trpcQuery.billing.customerPortal.useMutation();
  const { mutate: resumeSubscription } = trpcQuery.billing.resumeSubscription.useMutation();
  const featureGroup = useMemo(
    () =>
      featuresGroupFormatterConfigs(locale).reduce<FeatureFormatterDefinition[]>((acc, curr) => {
        const _acc = [...acc, ...curr.features];
        return _acc;
      }, []),
    [locale],
  );

  const { isFromThirdPartyPlatform, platform } = useSubscribePlatform();

  const formatBytesAsString = useMemo(
    () =>
      (bytes: number): string => {
        if (bytes === -1 || bytes === -2) {
          return t.pricing.features.unlimited;
        }
        return formatBytes(bytes);
      },
    [t],
  );

  const buttonGroup: UpgradeButtonProps[] = useMemo(() => {
    if (appEnv === 'SELF-HOSTED') return [];
    if (!subscription || subscription.plan === 'FREE') {
      return [
        {
          name: t.settings.space.upgrade,
          onClick: () => {
            spaceContext.showUIModal({ type: 'space-settings', tab: { type: 'SPACE_UPGRADE' } });
            localStorage.setItem('FROM_BILLING', 'true');
          },
          sx: {
            borderRadius: '50px',
            color: `var(${upgradeButtonTextColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
            // backgroundColor: `var(${subscribeButtonColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
            backgroundColor: `var(--bgStaticLightDefault) !important`,
            '&:hover': {
              backgroundColor: `var(--bgStaticLightHover) !important`,
            },
            '&:active': {
              backgroundColor: `var(--bgStaticLightActive) !important`,
            },
          },
        },
      ];
    }

    if (isFromThirdPartyPlatform) {
      return [
        {
          name: t.settings.billing.change_plan,
          sx: {
            borderRadius: '50px',
            color: `var(${upgradeButtonTextColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
            // backgroundColor: `var(${subscribeButtonColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
            backgroundColor: `var(--bgStaticLightDefault) !important`,
            '&:hover': {
              backgroundColor: `var(--bgStaticLightHover) !important`,
            },
            '&:active': {
              backgroundColor: `var(--bgStaticLightActive) !important`,
            },
          },
          onClick: () => {
            if (platform === 'APPSUMO') {
              window.open('https://appsumo.com/account/products/', '_blank');
            }
          },
        },
      ];
    }

    if (subscription.interval === 'once') {
      // Bika官方兑换码换取的一次性付款的订阅，没有续费按钮
      return [];
    }

    return [
      {
        name: t.settings.billing.change_payment_method,
        sx: {
          borderRadius: '50px',
        },
        color: 'neutral',
        variant: 'outlined',
        loading: loadingPortal,
        onClick: () => {
          // 这里应该是跳转到 Stripe 的页面
          getCustomerPortal(
            { spaceId },
            {
              onSuccess: (url) => {
                window.open(url, '_blank');
              },
            },
          );
        },
      },
      {
        name: t.settings.billing.change_plan,
        sx: {
          borderRadius: '50px',
          color: `var(${upgradeButtonTextColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
          // backgroundColor: `var(${subscribeButtonColor[spaceContext.data?.subscription?.plan || 'FREE']}) !important`,
          backgroundColor: `var(--bgStaticLightDefault) !important`,
          '&:hover': {
            backgroundColor: `var(--bgStaticLightHover) !important`,
          },
          '&:active': {
            backgroundColor: `var(--bgStaticLightActive) !important`,
          },
        },
        onClick: () => {
          spaceContext.showUIModal({ type: 'space-settings', tab: { type: 'SPACE_UPGRADE' } });
          localStorage.setItem('FROM_BILLING', 'true');
        },
      },
    ];
  }, []);

  const widgets: WidgetRenderVO[] = useMemo(() => {
    const retWidgets: WidgetRenderVO[] = [];
    if (entitlements) {
      // 小组件上的升级按钮
      const widgetUpgradeButton = compact([
        !isFromThirdPartyPlatform &&
          appEnv !== 'SELF-HOSTED' && {
            name: t.settings.space.upgrade,
            onClick: () => {
              if (platform === 'APPSUMO') {
                window.open('https://appsumo.com/account/products/', '_blank');
                return;
              }
              spaceContext.showUIModal({ type: 'space-settings', tab: { type: 'SPACE_UPGRADE' } });
              localStorage.setItem('FROM_BILLING', 'true');
            },
          },
      ]);
      for (const entitlement of entitlements) {
        if (isUsageFeature(entitlement)) {
          // 用量型小组件
          const featureFormatter = featureGroup.find((item) => item.key === entitlement.feature);
          if (!featureFormatter) continue;
          retWidgets.push({
            id: entitlement.feature,
            templateId: entitlement.feature,
            width: '50%',
            type: 'PROGRESS_BAR',
            name: featureFormatter.name,
            title: entitlement.feature,
            total: entitlement.max,
            used: entitlement.current,
            usedLabel: t.settings.billing.usages.used,
            unusedLabel: t.settings.billing.usages.unused,
            color: subscribeProcessColor[spaceContext.data?.subscription?.plan || 'FREE'],
            // trpc transformer will remove this 2 fields.
            formatter: entitlement.feature === 'STORAGES' ? formatBytesAsString : undefined,
            buttons: widgetUpgradeButton,
          });
        }
        if (isDateFeature(entitlement) || isFixedNumberFeature(entitlement)) {
          // 日期类型/固定数字类型
          const featureFormatter = featureGroup.find((item) => item.key === entitlement.feature);
          if (!featureFormatter) continue;
          retWidgets.push({
            type: 'SUBSCRIBE_INFO',
            id: entitlement.feature,
            templateId: entitlement.feature,
            width: '50%',
            title: entitlement.feature,
            name: featureFormatter.name,
            tips: featureFormatter.tips,
            content: () => (
              <div className="text-b3 text-color h-[54px] flex items-start">
                {featureFormatter.customFormat && (
                  <>{featureFormatter.customFormat(entitlement.value)}</>
                )}
              </div>
            ),
            buttons: widgetUpgradeButton,
          });
        }
        if (isSupportedFeature(entitlement)) {
          // 支持类型
          const featureFormatter = featureGroup.find((item) => item.key === entitlement.feature);
          if (!featureFormatter) continue;
          retWidgets.push({
            type: 'SUBSCRIBE_INFO',
            id: entitlement.feature,
            templateId: entitlement.feature,
            width: '50%',
            title: entitlement.feature,
            name: featureFormatter.name,
            tips: featureFormatter.tips,
            content: () => (
              <div className="text-b3 text-color h-[54px] flex items-start">
                {entitlement.active === true ? (
                  <>
                    <span className="mr-2 pt-[2px]">
                      <CheckOutlined
                        color={`var(${subscribeProcessColor[spaceContext.data?.subscription?.plan || 'FREE']})`}
                      />
                    </span>
                    {t.settings.billing.already_support}
                  </>
                ) : (
                  <>
                    <span className="mr-2 pt-[2px]">
                      <CloseOutlined
                        color={`var(${subscribeProcessColor[spaceContext.data?.subscription?.plan || 'FREE']})`}
                      />
                    </span>
                    {t.settings.billing.not_support}
                  </>
                )}
              </div>
            ),
            buttons: widgetUpgradeButton,
          });
        }
      }
    }
    return retWidgets;
  }, [
    entitlements,
    t,
    appEnv,
    featureGroup,
    spaceContext,
    platform,
    formatBytesAsString,
    isFromThirdPartyPlatform,
  ]);

  if (isLoading) {
    return (
      <>
        <NavHeader onClose={() => spaceContext.showUIModal(null)}>
          {t.settings.space.usage}
        </NavHeader>
        <Skeleton pos="SPACE_SETTING_PANEL"></Skeleton>
      </>
    );
  }

  const onClickCancelSubscription = () => {
    Modal.show({
      type: 'warning',
      title: t.settings.billing.cancel_subscription_title,
      content: t.settings.billing.cancel_subscription_content,
      onOk: async () => {
        cancelSubscription(
          { spaceId },
          {
            onSuccess: () => {
              toast(t.settings.billing.cancel_subscription_tips, {
                variant: 'success',
              });
              setTimeout(() => {
                spaceContext.refetch();
              }, 3000);
            },
          },
        );
      },
    });
  };

  const formatDate = (isoDate?: string) => {
    if (!isoDate) {
      return t.settings.billing.no_billing_day;
    }
    return dayjs(isoDate).tz(tz).format('L LT');
  };

  const renderCancelBlock = () => {
    if (subscription && subscription.cancelAtPeriodEnd) {
      return (
        <div>
          {t('settings.billing.will_cancel_subscribe', {
            expireAt: formatDate(subscription.expireAt),
          })}{' '}
          <span
            className="underline decoration-solid underline-offset-[2px] cursor-pointer text-[--textWarnDefault]"
            onClick={() => {
              resumeSubscription(
                { spaceId },
                {
                  onSuccess: () => {
                    toast('Resume successfully', {
                      variant: 'success',
                    });
                    spaceContext.refetch();
                  },
                },
              );
            }}
          >
            {t.settings.billing.resume_subscription}
          </span>
        </div>
      );
    }
    return (
      <p className="text-b3 text-[--text-secondary]">
        {t.settings.billing.you_will_lose_all_privileges}{' '}
        <span
          className="underline decoration-solid underline-offset-[2px] cursor-pointer"
          onClick={onClickCancelSubscription}
        >
          {t.settings.billing.cancel_subscribe}
        </span>
      </p>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <NavHeader onClose={() => spaceContext.showUIModal(null)}>{t.settings.space.usage}</NavHeader>
      <div className="px-[27px] overflow-y-auto flex-1">
        <SpaceBillingStatusComponent
          spaceId={spaceId}
          subscription={subscription}
          variant="space"
          buttons={buttonGroup}
        />
        {subscription &&
          subscription.plan !== 'FREE' &&
          !isFromThirdPartyPlatform &&
          appEnv !== 'SELF-HOSTED' && (
            <div className="mt-10">
              <div className="text-h7 text-[--text-secondary] mb-2">
                {t.settings.billing.cancel_subscribe}
              </div>
              {renderCancelBlock()}
            </div>
          )}
        <div className="mt-10">
          <div className="text-h7 text-[--text-secondary] mb-2">
            {t.settings.billing.usage_detail}
          </div>
          <PureDashboardRenderer locale={locale} widgets={widgets} />
        </div>
      </div>
    </div>
  );
}
