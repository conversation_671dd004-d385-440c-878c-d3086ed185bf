import { db, type Prisma, type ShareResourceType, ShareScope } from '@bika/server-orm';
import type { ShareSettings } from '@bika/types/node/bo';
import type { ShareUpsertDTO } from '@bika/types/node/dto';
import type { ShareVO } from '@bika/types/node/vo';
import { generateNanoID, generatePassword, meetPwdCriteria } from 'basenext/utils';
import { ShortURLSO } from '../../node/server/short-url-so';
import type { ShareModel } from './types';
/**
 * 分享对象
 */
export class ShareSO {
  private _model: ShareModel;

  private _shortUrl: ShortURLSO | undefined;

  private constructor(sharePO: ShareModel, shortUrl?: ShortURLSO) {
    this._model = sharePO;
    this._shortUrl = shortUrl;
  }

  get model() {
    return this._model;
  }

  get id(): string {
    return this.model.id;
  }

  get scope(): ShareScope {
    return this.model.scope;
  }

  get password(): string | null {
    return this.model.password;
  }

  get resourceId(): string {
    return this.model.resourceId;
  }

  get resourceType(): ShareResourceType {
    return this.model.resourceType;
  }

  get isNew() {
    return (
      this.model.createdAt.getTime() === this.model.updatedAt.getTime() &&
      this.model.createdBy === this.model.updatedBy
    );
  }

  get settings(): ShareSettings | undefined {
    return (this.model.settings as ShareSettings) || undefined;
  }

  get needAuth(): boolean {
    return !this.isPublicShare(this.scope);
  }

  toVO(): ShareVO {
    const vo = {
      resourceId: this.model.resourceId,
      resourceType: this.resourceType,
      scope: this.model.scope,
      password: this.model.password ?? undefined,
      settings: this.settings,
      shortUrlId: this._shortUrl?.id,
      id: this.model.id,
    };
    return vo;
  }

  isPublicShare(scope: ShareScope): boolean {
    return (
      scope === ShareScope.PUBLIC_READ ||
      scope === ShareScope.PUBLIC_READ_WRITE ||
      scope === ShareScope.ANONYMOUS_READ_WRITE
    );
  }

  async updateScope(param: Pick<ShareModel, 'scope' | 'updatedBy'>): Promise<void> {
    // 如果是<公开>转<非公开>,需删除密码
    let data: Prisma.ShareUpdateInput = { ...param };
    if (this.isPublicShare(this.scope) && !this.isPublicShare(param.scope)) {
      data = { ...data, password: null };
    }
    this._model = await db.prisma.share.update({
      where: {
        id: this.model.id,
      },
      data,
    });
  }

  validatePassword(password: string): boolean {
    return this.password != null && this.password === password;
  }

  async createPassword(userId: string): Promise<void> {
    const password = generatePassword(8);
    this._model = await db.prisma.share.update({
      where: {
        id: this.model.id,
      },
      data: {
        password,
        updatedBy: userId,
      },
    });
  }

  async updatePassword(userId: string, password: string): Promise<void> {
    const meets = password.length >= 8 && password.length <= 16 && meetPwdCriteria(password);
    if (!meets) {
      throw new Error(
        'Password length must be between 8 and 16 characters, and include at least two of the following: letters, numbers, and symbols',
      );
    }
    this._model = await db.prisma.share.update({
      where: {
        id: this.model.id,
      },
      data: {
        password,
        updatedBy: userId,
      },
    });
  }

  async deletePassword(userId: string): Promise<void> {
    this._model = await db.prisma.share.update({
      where: {
        id: this.model.id,
      },
      data: {
        password: null,
        updatedBy: userId,
      },
    });
  }

  static async findUnique(
    data: Pick<ShareModel, 'resourceId' | 'resourceType'>,
  ): Promise<ShareSO | null> {
    const shareModel = await db.prisma.share.findUnique({
      where: {
        resourceId_resourceType: {
          resourceId: data.resourceId,
          resourceType: data.resourceType,
        },
      },
    });
    return shareModel && new ShareSO(shareModel);
  }

  static async create(
    data: Pick<ShareModel, 'resourceId' | 'resourceType' | 'scope' | 'createdBy'>,
  ): Promise<ShareSO> {
    const sharePO = await db.prisma.share.create({
      data: {
        id: generateNanoID('shr'),
        ...data,
        updatedBy: data.createdBy,
      },
    });
    return new ShareSO(sharePO);
  }

  static async upsert(userId: string, data: ShareUpsertDTO['data']): Promise<ShareSO> {
    const share = await db.prisma.share.upsert({
      where: {
        resourceId_resourceType: {
          resourceId: data.resourceId,
          resourceType: data.resourceType,
        },
      },
      update: {
        ...data,
        updatedBy: userId,
      },
      create: {
        id: generateNanoID('shr'),
        ...data,
        createdBy: userId,
        updatedBy: userId,
      },
    });
    return new ShareSO(share);
  }

  static async init(id: string): Promise<ShareSO | null> {
    const share = await db.prisma.share.findUnique({
      where: {
        id,
      },
    });
    if (!share) {
      return null;
    }
    return new ShareSO(share);
  }

  static async findByResourceId(
    resourceId: string,
    resourceType: ShareResourceType,
  ): Promise<ShareSO | null> {
    const share = await db.prisma.share.findUnique({
      where: { resourceId_resourceType: { resourceId, resourceType } },
    });
    if (!share) {
      return null;
    }
    const shortURLSos = await ShortURLSO.findByRelationIds([resourceId]);
    const shortUrl = shortURLSos.get(resourceId);
    return new ShareSO(share, shortUrl);
  }
}
