import { <PERSON>de<PERSON><PERSON>roller } from '@bika/domains/node/apis';
import { SpaceAuditLogSO } from '@bika/domains/system/server/audit/space-audit-log-so';
import type { RoleSO } from '@bika/domains/unit/server/role-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, publicProcedure, router } from '@bika/server-orm/trpc';
import type { AIChatShareScope } from '@bika/types/ai/bo';
import { ShareResourceTypeSchema, ShortURLRelationTypeSchema } from '@bika/types/node/bo';
import * as T from '@bika/types/node/dto';
import { CollaboratorPaginationVOSchema } from '@bika/types/permission/vo';
import { z } from 'zod';
import { AIChatSO } from '../../ai/server/ai-chat/ai-chat-so';
import { AISearchSO } from '../../ai/server/ai-search-so';
import { ShareSO } from '../../permission/server/share-so';
import { ShareController } from './share-controller';

/**
 * Node share router
 */
export const nodeShareRouter = router({
  /**
   * Retrieve the share info of a node
   */
  info: protectedProcedure.input(T.NodeShareInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const nodeAclSO = await node.toAclSO();
    return nodeAclSO.toVO({ locale: user.locale });
  }),

  getByResource: protectedProcedure
    .input(
      z.object({
        resourceId: z.string().describe('share resource id'),
        resourceType: ShareResourceTypeSchema,
      }),
    )
    .query(async (opts) => {
      const { input } = opts;
      const { resourceId, resourceType } = input;
      const share = await ShareSO.findByResourceId(resourceId, resourceType);
      return share?.toVO() ?? null;
    }),

  detailInfo: publicProcedure
    .input(
      z.object({
        id: z.string().describe('share id'),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session || {};
      return ShareController.retrieveInfo(input.id, userId);
    }),

  /**
   * Retrieve the collaborators of a node
   */
  collaborators: protectedProcedure
    .input(T.NodeCollaboratorListDTOSchema)
    .output(CollaboratorPaginationVOSchema)
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const { spaceId, id: nodeId, pageNo, pageSize } = input;
      const member = await user.getMember(spaceId);
      const space = await member.getSpace();
      const node = await space.getNode(nodeId);
      const nodeAclSO = await node.toAclSO();
      return nodeAclSO.collaborators({ pageNo, pageSize });
    }),
  /**
   * Update the share scope of a node
   */
  updateScope: protectedProcedure.input(T.NodeShareUpdateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId, scope } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const nodeAclSO = await node.toAclSO();
    await nodeAclSO.operator(user.id).updateShareScope(scope);
    // 记录审计事件
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'share.scope.update',
      id: node.id,
      name: JSON.stringify(node.name),
      scope,
    });
  }),

  upsert: protectedProcedure.input(T.ShareUpsertDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const share = await ShareController.upsert(user, input);
    const { spaceId, data } = input;

    // space log
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId,
      type: 'share.upsert',
      resourceId: data.resourceId,
      resourceType: data.resourceType,
      scope: data.scope,
      settings: data.settings ? JSON.stringify(data.settings) : undefined,
      isNew: share.isNew,
    });
    return share.toVO();
  }),
  /**
   * Restore share config of a node
   */
  restore: protectedProcedure.input(T.NodeShareRestoreDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, id: nodeId } = input;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const node = await space.getNode(nodeId);
    const nodeAclSO = await node.toAclSO();
    await nodeAclSO.operator(user.id).restore();
    // 记录审计事件
    SpaceAuditLogSO.createFromRequestContext(ctx, {
      spaceId: space.id,
      type: 'share.restore',
      id: node.id,
      name: JSON.stringify(node.name),
    });
  }),
  /**
   * Create a password for a sharing node
   */
  createPassword: protectedProcedure
    .input(T.NodeSharePasswordCreateDTOSchema)
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const { spaceId, id: nodeId } = input;
      const member = await user.getMember(spaceId);
      const space = await member.getSpace();
      const node = await space.getNode(nodeId);
      const nodeAclSO = await node.toAclSO();
      await nodeAclSO.operator(user.id).createSharePassword();
      // 记录审计事件
      SpaceAuditLogSO.createFromRequestContext(ctx, {
        spaceId: space.id,
        type: 'share.password.create',
        id: node.id,
        name: JSON.stringify(node.name),
      });
    }),
  /**
   * Update the password of a sharing node
   */
  updatePassword: protectedProcedure
    .input(T.NodeSharePasswordUpdateDTOSchema)
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const { spaceId, id: nodeId, password } = input;
      const member = await user.getMember(spaceId);
      const space = await member.getSpace();
      const node = await space.getNode(nodeId);
      const nodeAclSO = await node.toAclSO();
      await nodeAclSO.operator(user.id).updateSharePassword(password);
      // 记录审计事件
      SpaceAuditLogSO.createFromRequestContext(ctx, {
        spaceId: space.id,
        type: 'share.password.update',
        id: node.id,
        name: JSON.stringify(node.name),
      });
    }),
  /**
   * Delete the password of a sharing node
   */
  deletePassword: protectedProcedure
    .input(T.NodeSharePasswordDeleteDTOSchema)
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const { spaceId, id: nodeId } = input;
      const member = await user.getMember(spaceId);
      const space = await member.getSpace();
      const node = await space.getNode(nodeId);
      const nodeAclSO = await node.toAclSO();
      await nodeAclSO.operator(user.id).deleteSharePassword();
      // 记录审计事件
      SpaceAuditLogSO.createFromRequestContext(ctx, {
        spaceId: space.id,
        type: 'share.password.delete',
        id: node.id,
        name: JSON.stringify(node.name),
      });
    }),
  /**
   * Grant permission for a member unit to a node
   */
  grantPermission: protectedProcedure.input(T.NodeGrantPermissionSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    await NodeController.grantNodePermissionByMember(ctx, input);
  }),

  /**
   * Revoke permission for a member unit to a node
   */
  revokePermission: protectedProcedure
    .input(T.NodeRevokePermissionSchema)
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const { spaceId, id: nodeId, unitId } = input;
      const member = await user.getMember(spaceId);
      const space = await member.getSpace();
      const node = await space.getNode(nodeId);
      const unit = await space.getUnit(unitId);
      const nodeAclSO = await node.toAclSO();
      await nodeAclSO.operator(user.id).revoke(unit);
      // 记录审计事件
      SpaceAuditLogSO.createFromRequestContext(ctx, {
        spaceId: space.id,
        type: 'share.revoke',
        id: node.id,
        name: JSON.stringify(node.name),
        unit: unit.isRole ? JSON.stringify((unit as RoleSO).getName()) : unit.getName(user.locale),
      });
    }),

  toggleShortURL: protectedProcedure
    .input(
      z.object({
        relationId: z.string().describe('relation id'),
        relationType: ShortURLRelationTypeSchema,
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      if (input.relationType === 'NODE_RESOURCE') {
        return NodeController.toggleShortURL(ctx, { nodeId: input.relationId });
      }
      if (input.relationType === 'AI_CHAT') {
        return ShareController.toggleShortURL(input.relationId, input.relationType);
      }
      throw new Error('Invalid relation type');
    }),
});
