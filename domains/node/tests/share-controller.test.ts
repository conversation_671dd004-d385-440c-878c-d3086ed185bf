import type { UserSO } from '@bika/domains/user/server/user-so';
import { db } from '@bika/server-orm';
import type { AIIntentParams } from '@bika/types/ai/bo';
import { sleep } from 'sharelib/sleep';
import { afterEach, describe, expect, test } from 'vitest';
import { MockContext } from '../../__tests__/mock';
import { AIChatSO } from '../../ai/server/ai-chat/ai-chat-so';
import { AIMockSO } from '../../ai/server/ai-mock/ai-mock-so';
import { AISearchSO } from '../../ai/server/ai-search-so';
import { ShareController } from '../apis/share-controller';
import type { FolderSO } from '../server/folder-so';

describe('share ai chat history ', () => {
  beforeAll(async () => {
    await db.search.deleteIndex('AI_PUBLISHED_CHAT', 'exact');
  });

  afterEach(async () => {
    AIMockSO.clearFilters();
  });

  const createAINode = async (user: UserSO, folder: FolderSO) => {
    const aiNodeSO = await folder.createChildSimple(user, {
      resourceType: 'AI',
      name: 'ai-node',
    });
    return aiNodeSO;
  };

  const initChat = async (user: UserSO, intent: AIIntentParams): Promise<AIChatSO> => {
    const chat = await AIChatSO.newChatByUser(user.id, intent);
    AIMockSO.addFilter({
      check: async (_) => true,
      doStream: 'Hi there',
    });

    await AIMockSO.sandbox(async () => {
      await chat.message(MockContext.createMockRequestContext(user), 'hello ');
    });

    return chat;
  };

  test('ai-node-chat: create share for ai chat history, scope is PUBLIC_READ', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const aiNodeSO = await createAINode(user, rootFolder);
    const chat = await initChat(user, {
      type: 'AI_NODE',
      nodeId: aiNodeSO.id,
    });

    const share = await ShareController.upsert(user, {
      spaceId: rootFolder.spaceId,
      data: {
        resourceId: chat.id,
        resourceType: 'AI_CHAT',
        scope: 'PUBLIC_READ',
        settings: undefined,
      },
    });
    expect(share.isNew).toBe(true);
    const nodeVO = await aiNodeSO.toVO();
    // only share the chat node, not the ai node
    expect(nodeVO.sharing).toBe(false);

    const newChat = await AIChatSO.init(chat.id);

    const chatVO = await newChat.toVO();
    expect(chatVO.share?.scope).toBe('PUBLIC_READ');
    // sleep 1 second
    await sleep(1000);
    const publishedChat = await AISearchSO.searchPublishedAIChats({
      pageNo: 1,
      pageSize: 1,
    });
    expect(publishedChat.data.length).toBe(0);
    expect(publishedChat.pagination.total).toBe(0);

    // get published chat
    const listChats = await AIChatSO.page(user, {
      pageNo: 1,
      pageSize: 1,
      type: 'AI_NODE',
      nodeId: aiNodeSO.id,
      shareScope: 'PUBLISH',
    });
    expect(listChats.data.length).toBe(0);
  });

  test('ai-node-chat: update share for ai chat history, scope is DEFAULT', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const aiNodeSO = await createAINode(user, rootFolder);
    const chat = await initChat(user, {
      type: 'AI_NODE',
      nodeId: aiNodeSO.id,
    });
    await ShareController.upsert(user, {
      spaceId: rootFolder.spaceId,
      data: {
        resourceId: chat.id,
        resourceType: 'AI_CHAT',
        scope: 'PUBLIC_READ',
        settings: undefined,
      },
    });

    const share = await ShareController.upsert(user, {
      spaceId: rootFolder.spaceId,
      data: {
        resourceId: chat.id,
        resourceType: 'AI_CHAT',
        scope: 'DEFAULT',
        settings: undefined,
      },
    });
    expect(share.isNew).toBe(false);
    expect(share.scope).toBe('DEFAULT');

    const newChat = await AIChatSO.init(chat.id);
    const chatVO = await newChat.toVO();
    expect(chatVO.share?.scope).toBe('DEFAULT');
    await sleep(1000);
    // check index
    const publishedChat = await AISearchSO.searchPublishedAIChats({
      pageNo: 1,
      pageSize: 1,
    });
    expect(publishedChat.data.length).toBe(0);
    expect(publishedChat.pagination.total).toBe(0);

    // get published chat
    const listChats = await AIChatSO.page(user, {
      pageNo: 1,
      pageSize: 1,
      type: 'AI_NODE',
      nodeId: aiNodeSO.id,
      shareScope: 'PUBLISH',
    });
    expect(listChats.data.length).toBe(0);
  });

  test('ai-node-chat: update share for ai chat history, scope is PUBLISH', async () => {
    const { user, rootFolder } = await MockContext.initUserContext();
    const aiNodeSO = await createAINode(user, rootFolder);
    const chat = await initChat(user, {
      type: 'AI_NODE',
      nodeId: aiNodeSO.id,
    });
    await ShareController.upsert(user, {
      spaceId: rootFolder.spaceId,
      data: {
        resourceId: chat.id,
        resourceType: 'AI_CHAT',
        scope: 'PUBLIC_READ',
        settings: {
          toCommunity: true,
        },
      },
    });
    const newChat = await AIChatSO.init(chat.id);
    const chatVO = await newChat.toVO();
    expect(chatVO.share?.scope).toBe('PUBLISH');
    await sleep(1000);
    const publishedChat = await AISearchSO.searchPublishedAIChats({
      pageNo: 1,
      pageSize: 1,
      keyword: 'He',
    });
    expect(publishedChat.data.length).toBe(1);
    expect(publishedChat.pagination.total).toBe(1);

    // get published chat
    const listChats = await AIChatSO.page(user, {
      pageNo: 1,
      pageSize: 1,
      type: 'AI_NODE',
      nodeId: aiNodeSO.id,
      shareScope: 'PUBLISH',
    });
    expect(listChats.data.length).toBe(1);
  });
});
