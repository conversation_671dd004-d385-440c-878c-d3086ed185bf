import { db } from '@bika/server-orm';
import { adminProcedure } from '@bika/server-orm/trpc';
import { RecordListDTOSchema } from '@bika/types/database/dto';
import { z } from 'zod';
import { AISearchSO } from '../../ai/server/ai-search-so';
import { AdminSearchPageTypeSchema, AdminSearchSO } from '../server/admin-search-so';

export const adminSearchIndexSubRouter = {
  // database vo and records
  database: adminProcedure.query(async () => AdminSearchSO.getDatabase()),
  records: adminProcedure.input(RecordListDTOSchema).query(async (opts) => {
    const { input } = opts;
    const dto = input;
    return AdminSearchSO.getRecords(dto);
  }),
  view: adminProcedure
    .input(AdminSearchPageTypeSchema)
    .query(async (opts) => AdminSearchSO.getView(opts.input)),

  rebuildIndex: adminProcedure
    .input(z.object({ index: AdminSearchPageTypeSchema }))
    .mutation(async (opts) => {
      const { input } = opts;
      if (input.index === 'INDICES') {
        await Promise.allSettled([
          AISearchSO.rewriteAllNodeSearchIndex(50),
          AISearchSO.rewriteAllAIChatsIndex(),
        ]);
      } else if (input.index === 'AI_PUBLISHED_CHAT') {
        await AISearchSO.rewriteAllAIChatsIndex();
      }
    }),
  cleanIndexes: adminProcedure
    .input(z.object({ index: AdminSearchPageTypeSchema }))
    .mutation(async (opts) => {
      const { input } = opts;
      if (input.index === 'INDICES') {
        await AISearchSO.cleanIndexes();
      }
      if (input.index === 'AI_PUBLISHED_CHAT') {
        await db.search.deleteIndex('AI_PUBLISHED_CHAT', 'exact');
      }
    }),
};
