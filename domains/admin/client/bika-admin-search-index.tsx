import { useApiCaller } from '@bika/api-caller';
import { Modal } from '@bika/ui/modal';
import Button from '@mui/joy/Button';
import type React from 'react';
import { CrudDatabaseManager } from '../../database/client/database/crud-database-manager';
import type { AdminSearchPageType } from '../server/admin-search-so';

export function BikaAdminSearchIndex() {
  const { trpc, trpcQuery } = useApiCaller();

  const { mutateAsync: doRebuild } = trpcQuery.admin.searchIndex.rebuildIndex.useMutation();
  const { mutateAsync: doClean } = trpcQuery.admin.searchIndex.cleanIndexes.useMutation();

  // Database
  const { data: databaseVO } = trpcQuery.admin.searchIndex.database.useQuery();

  return (
    <>
      {databaseVO && (
        <CrudDatabaseManager
          value={databaseVO}
          getRecord={() => null!}
          listRecords={async (dto) => trpc.admin.searchIndex.records.query(dto)}
          getView={(viewId) =>
            trpcQuery.admin.searchIndex.view.useQuery(viewId as AdminSearchPageType)
          }
          useRecordMutation={() => ({
            createRecord: async () => null!,
            updateRecord: async () => null!,
            updateRecords: async () => null!,
            deleteRecords: async () => {},
            isMutating: () => false,
          })}
          disableDefaultToolbar={true}
          beforeLeftToolbar={(view) => (
            <>
              <Button
                onClick={() => {
                  Modal.show({
                    type: 'warning',
                    title: '重建索引',
                    content: '确认要重新索引吗？留意服务端日志，会在后台进行，遍历所有资源',
                    onOk: async () => {
                      doRebuild({ index: view.id as AdminSearchPageType });
                    },
                  });
                }}
              >
                {view?.id === 'INDICES' ? '重建所有索引' : '重建当前索引'}
              </Button>
              <Button
                onClick={() => {
                  Modal.show({
                    type: 'warning',
                    title: '清理残留索引',
                    content:
                      '会对Search引擎，做全盘扫描，找出DB里没有的索引，然后删除，通常是不需要这样做的，程序里在删除时自动删除，但是如果有问题，可以尝试这个',
                    onOk: async () => {
                      doClean({ index: view.id as AdminSearchPageType });
                    },
                  });
                }}
              >
                {view?.id === 'INDICES' ? '清理所有残留索引' : '清理当前残留索引'}
              </Button>
            </>
          )}
          customCreateRecordComponent={(_closeModal: () => void): React.ReactNode => (
            <>TODO: Create</>
          )}
        />
      )}
    </>
  );
}
