'use client';

import type { FirstDataRenderedEvent, PaginationChangedEvent } from '@ag-grid-community/core';
import { useTRPCQuery } from '@bika/api-caller';
import { useLocale } from '@bika/contents/i18n/context';
import { useRemoteStorage } from '@bika/domains/system/client/hooks/useRemoteStorage';
import type { Database, View, ViewField, ViewFilter } from '@bika/types/database/bo';
import { useDatabaseVOContext } from '@bika/types/database/context';
import type { DatabaseLookupFieldRO } from '@bika/types/database/ro';
import type { RecordRenderVO, ViewVO } from '@bika/types/database/vo';
import { useNodePermissionContext } from '@bika/types/node/context';
import { useShareContext, useSpaceContextForce } from '@bika/types/space/context';
import type {
  RemoteStoragePropertyDatabaseViewColWidth,
  RemoteStoragePropertyDatabaseViewFilter,
  RemoteStoragePropertyDatabaseViewHiddenColumn,
  RemoteStoragePropertyDatabaseViewRowGroup,
  RemoteStoragePropertyDatabaseViewSort,
} from '@bika/types/system/remote-storage';
import { useGlobalContext } from '@bika/types/website/context';
import type { GridApi } from '@bika/ui/database/types';
import { getRecordDTOFromRecordDetailVO } from '@bika/ui/form/utils';
import { useUIFrameworkContext } from '@bika/ui/framework';
import { Skeleton } from '@bika/ui/skeleton';
import { useBGridCollaborationStore, useSnackBar } from '@bika/ui/snackbar';
import { useDebounce } from 'ahooks';
import { produce } from 'immer';
import _ from 'lodash';
import React, {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import type { ICustomToolbar } from '../database/interface';
import { useRowHeight, ViewHeaderToolbar } from '../database/view-header-toolbar';
import {
  BGrid,
  type BGridMenuEvent,
  type BGridRefHandle,
  type IColumnSortState,
} from './ag-grid/bgrid';
import { checkedExistedRowGroup } from './ag-grid/utils';
import { AgGridConfig } from './ag-grid-config';

const { isEqual } = _;

export type TableViewRendererRefHandle = Omit<BGridRefHandle, 'getApi'> & {
  getApi?: () => GridApi<RecordRenderVO> | undefined;
};

interface Props extends ICustomToolbar {
  spaceId: string;
  databaseId: string;
  view: ViewVO;
  mirrorId?: string;
  disableEditing?: boolean;
  disableOperateColumn?: boolean;
  pagination?: boolean;
  isTemplatePreview?: boolean;
  className?: string;
  hasSequenceColumn?: boolean;
}
export const TableViewRendererBase = forwardRef<TableViewRendererRefHandle, Props>((props, ref) => {
  const { spaceId, databaseId, className, mirrorId, view, pagination } = props;
  const viewId = view.id;
  const { rowHeight } = useRowHeight(mirrorId ?? viewId);

  const api = useDatabaseVOContext();
  const bGridRef = React.useRef<BGridRefHandle>(null);
  const { searchParams } = useUIFrameworkContext();
  const { authContext: auth, showUIModal } = useGlobalContext();

  const spaceContext = useSpaceContextForce();

  // const searchQueryText = searchParams.get('search') ?? '';
  // Search Keywords
  const [searchKeyword, setSearchKeyword] = React.useState<string>(
    () => searchParams.get('search') ?? '',
  );

  const keyword = useDebounce(searchKeyword, { wait: 300 });

  // 隐藏列维护,从远程获取
  const [hiddenColumnsData, setHiddenColumnsData] =
    useRemoteStorage<RemoteStoragePropertyDatabaseViewHiddenColumn>(
      'DATABASE_VIEW_HIDDEN_COLUMNS',
      viewId,
    );

  // 个人用户的filter信息，从远程获取
  const [filterData] = useRemoteStorage<RemoteStoragePropertyDatabaseViewFilter>(
    'DATABASE_VIEW_FILTER',
    mirrorId ?? viewId,
  );

  const [fieldsWidthItem, setFieldsWidth] =
    useRemoteStorage<RemoteStoragePropertyDatabaseViewColWidth>('DATABASE_VIEW_COL_WIDTH', viewId);

  const fieldWidths = fieldsWidthItem?.fields ?? [];

  const [viewSortData] = useRemoteStorage<RemoteStoragePropertyDatabaseViewSort>(
    'DATABASE_VIEW_SORT',
    mirrorId ?? viewId,
  );

  const [rowGroupData] = useRemoteStorage<RemoteStoragePropertyDatabaseViewRowGroup>(
    'DATABASE_VIEW_ROW_GROUP',
    mirrorId ?? viewId,
  );

  const viewRowGroup = rowGroupData?.data;

  const filter = filterData?.filters;
  const viewSort = viewSortData?.sort;

  const [tempFieldList, setTempFieldList] = useState<ViewField[] | undefined>(undefined);

  const trpcQuery = useTRPCQuery();

  const { data: fields } = trpcQuery.database.getFieldROs.useQuery(
    {
      databaseId: databaseId as string,
    },
    {
      enabled: databaseId !== undefined,
      meta: { skipError: true },
    },
  );

  const combinedView: ViewVO = useMemo(() => {
    if (!fields) {
      return view;
    }

    const newFields = view.columns
      .map((field) => {
        const foundField = fields.find((_field) => _field.id === field.id);
        if (!foundField) {
          return field;
        }
        return {
          ...field,
          property: foundField?.property ?? field.property,
          render: (foundField as DatabaseLookupFieldRO)?.render,
        };
      })
      .filter(Boolean);

    return {
      ...view,
      columns: newFields,
    } as ViewVO;
  }, [view, fields]);

  useEffect(() => {
    if (!tempFieldList) {
      return;
    }
    const newTempFieldList = produce(tempFieldList, (draft) => {
      // each comobinedview.columns
      for (let k = 0; k < combinedView.columns.length; k++) {
        const currentColumn = combinedView.columns[k];
        if (!tempFieldList.find((item) => item.id === currentColumn.id)) {
          draft.push(currentColumn);
        }
      }
    });
    setTempFieldList(newTempFieldList);
  }, [combinedView, tempFieldList]);
  // const [refreshing, setRefreshing] = React.useState(false);
  const locale = useLocale();
  const { t } = useLocale();
  const { toast } = useSnackBar();
  const queryPageSize = searchParams.get('pageSize') || '100';
  const queryPage = searchParams.get('page') || '1';

  // const [tz] = useLocalStorageState<string>(consts.CONST_STORAGE_KEY_TIMEZONE);

  const pageSize = parseInt(queryPageSize, 10);

  const cachedBlockSize = pageSize < 100 ? pageSize : AgGridConfig.cachedBlockSize;

  const page = parseInt(queryPage, 10);

  // const colors = useCssColor();
  const handlePage = useCallback(
    (curPage: number) => {
      searchParams.set('page', curPage ? curPage.toString() : undefined);
    },
    [searchParams],
  );

  const handleSort = useCallback(
    (term: string) => {
      searchParams.set('sort', term);
    },
    [searchParams],
  );

  const strSort = searchParams.get('sort');
  const theSort: IColumnSortState[] | undefined = useMemo(() => {
    if (strSort) {
      try {
        const item = strSort.split(',')[0];
        const first = item.split('.');
        if (first[0] && first[1]) {
          const state: IColumnSortState[] = [
            {
              colId: decodeURIComponent(first[0]),
              sort: first[1] as 'asc' | 'desc',
            },
          ];
          return state;
          // params.api!.applyColumnState({
          //   state,
          //   defaultState: { sort: null },
          // });
        }
      } catch {
        console.error('catch');
      }
    }
    return undefined;
  }, [strSort]);

  const setPageSize = useCallback(
    (curPageSize: number) => {
      searchParams.set('pageSize', curPageSize ? curPageSize.toString() : undefined);
    },
    [searchParams],
  );

  const { sharing } = useShareContext();
  const permission = useNodePermissionContext();

  const tipSharingLogin = useCallback(() => {
    if (!sharing) return;
    if (permission && permission.privilege.privilege === 'CAN_VIEW' && !permission.needLogin)
      return;
    if (auth.isLogin) return;
    toast(
      <div>
        You can only view files when not logged in, please try again after logging in{' '}
        <span
          className={'underline underline-offset-2 ml-3 cursor-pointer'}
          onClick={() => {
            showUIModal({
              name: 'AUTH',
              redirect: window.location.href,
              onLoginAction: 'CLOSE_MODAL',
              // onLogin: () => void context.showUIModal(null),
            });
          }}
        >
          {t.auth.login}
        </span>
      </div>,
      {
        variant: 'warning',
        preventDuplicate: true,
        id: 'tip_login',
      },
    );
  }, [sharing, permission, auth.isLogin, showUIModal, toast]);

  const editable = useBGridCollaborationStore((state) => state.editable);

  const disableEditing = useMemo(() => {
    // if (!permission) return true;
    if (!editable) return true;
    if (permission && permission.privilege.privilege === 'CAN_VIEW') return true;
    if (props.disableEditing) return true;

    return !auth.isLogin;
  }, [auth.isLogin, permission, editable, props.disableEditing]);

  // /*
  //  * 请求别人新增数据
  //  */
  // async function onClickShowRequestCreateRecordModal() {
  //   spaceContext.showUIModal({ type: 'request-record-mission', databaseId, mirrorId, viewId });
  // }

  const columns = useMemo(
    () => combinedView?.columns?.filter((_col) => _col.hidden !== true) ?? [],
    [combinedView],
  );

  const ensureStorageRowGroup = viewRowGroup && checkedExistedRowGroup(viewRowGroup, columns);

  const storageRowGroupOrTemplateRowGroup =
    ensureStorageRowGroup &&
    Array.isArray(ensureStorageRowGroup) &&
    ensureStorageRowGroup.length > 0
      ? ensureStorageRowGroup
      : view.groups;

  const customFieldViews = hiddenColumnsData?.fields;

  // make sure all columns
  const checkedCustomFieldViews = useMemo(() => {
    if (!customFieldViews) {
      return columns.map((field) => ({
        ...field,
        hidden: field.hidden ?? false,
      }));
    }

    return columns.map((column) => {
      const hiddenColumn = customFieldViews.find((field) => field.id === column.id);
      return {
        ...column,
        hidden: hiddenColumn?.hidden ?? false,
      };
    });
  }, [columns, customFieldViews]);

  const setHiddenColumns = useCallback(
    (viewFields: ViewField[]) => {
      const rsData: RemoteStoragePropertyDatabaseViewHiddenColumn = {
        type: 'DATABASE_VIEW_HIDDEN_COLUMNS',
        viewId: mirrorId ?? viewId,
        fields: viewFields,
      };
      setHiddenColumnsData(rsData);
    },
    [setHiddenColumnsData, viewId, mirrorId],
  );

  // const setFilter = useCallback(
  //   (_filter: ViewFilter) => {
  //     const filterRSValue: RemoteStoragePropertyDatabaseViewFilter = {
  //       type: 'DATABASE_VIEW_FILTER',
  //       viewId: mirrorId ?? viewId,
  //       filters: _filter,
  //     };
  //     setFilterData(filterRSValue);
  //   },
  //   [setFilterData, viewId, mirrorId],
  // );

  // const setViewSort = useCallback(
  //   (_sort: ViewSortArray) => {
  //     const filterRSValue: RemoteStoragePropertyDatabaseViewSort = {
  //       type: 'DATABASE_VIEW_SORT',
  //       viewId: mirrorId ?? viewId,
  //       sort: _sort,
  //     };
  //     setRemoteViewSort(filterRSValue);
  //   },
  //   [setRemoteViewSort, viewId, mirrorId],
  // );

  const refreshGrid = useCallback((filterInfo: ViewFilter, kw: string) => {
    // bGridRef 未实现filterInfo, keyword) 参数
    bGridRef.current?.refresh(filterInfo, kw);
  }, []);

  React.useEffect(() => {
    // 注册服务端推送事件，刷新refresh-database
    if (!filter) return;
    auth.registerSseHandler('refresh-grid', () => {
      refreshGrid(filter, keyword);
    });
    auth.unregisterSseHandler('refresh-grid');
  }, [auth, filter, keyword, refreshGrid]);

  const handlePaginationChanged = useCallback(
    (params: PaginationChangedEvent<RecordRenderVO>) => {
      if (params.newPage) {
        const currentPage = params.api.paginationGetCurrentPage();
        handlePage(currentPage + 1);
      }
      if (params.newPageSize) {
        const curPageSize = params.api.paginationGetPageSize();
        setPageSize?.(curPageSize);
      }
    },
    [handlePage, setPageSize],
  );

  const onFirstDataRendered = useCallback(
    (params: FirstDataRenderedEvent<RecordRenderVO>) => {
      params.api.paginationGoToPage(page - 1);
      params.api?.sizeColumnsToFit({
        defaultMinWidth: 120,
      });
    },
    [page],
  );

  const { useRecordMutation } = useDatabaseVOContext();
  const { createRecord } = useRecordMutation();

  const { data: databaseBO } = trpcQuery.node.boInfo.useQuery(
    { id: databaseId! },

    {
      enabled: databaseId !== undefined,

      meta: { skipError: true },
    },
  );
  const database = databaseBO as Database;

  // const [globalApi, _setGlobalAGGridApi] = useGlobalState<GridApi<RecordRenderVO>>('AG_GRID_API');

  const viewBo: View | undefined = database?.views?.find(
    (item) => item.id === viewId || item.templateId === viewId,
  );

  const utils = trpcQuery.useUtils();
  // 处理，view更新的事件，转译一下AGGrid的接口
  const handleUpdateViewFieldsSeq = useCallback(
    async (newColIdLists: string[]) => {
      if (!(permission && permission.privilege.privilege === 'FULL_ACCESS')) return;
      if (!viewBo) {
        return;
      }
      const previousColumns = combinedView?.columns ?? [];
      const databaseFields = tempFieldList != null ? tempFieldList : (previousColumns ?? []);

      // sortedColIds 缺少 hidden 的字段
      const sortedColIds = newColIdLists.filter((colId) => colId.startsWith('fld'));
      const newFieldList: ViewField[] = [];
      let hiddenCount = 0;
      for (let k = 0; k < databaseFields.length; k++) {
        const currentFieldId = databaseFields[k].id as string;
        const currentSortColId = sortedColIds[k - hiddenCount];
        if (sortedColIds.includes(currentFieldId) && currentFieldId === currentSortColId) {
          newFieldList.push(databaseFields[k]);
        } else if (!sortedColIds.includes(currentFieldId)) {
          // hidden field
          hiddenCount++;
          newFieldList.push(databaseFields[k]);
        } else if (sortedColIds.includes(currentFieldId) && currentFieldId !== currentSortColId) {
          // 拖拽的 field 顺序有改动
          const currentField = databaseFields.find((field) => field.id === currentSortColId);
          newFieldList.push(currentField as ViewField);
        }
      }

      if (isEqual(databaseFields, newFieldList)) {
        return;
      }

      await api.updateView({
        databaseId,
        viewId,
        data: {
          ...viewBo,
          fields: newFieldList.filter(Boolean),
        },
      });
      setTempFieldList(newFieldList.filter(Boolean));
      await utils.node.boInfo.invalidate();
    },
    [viewBo, viewId, databaseId, permission, tempFieldList, combinedView, api, utils.node.boInfo],
  );

  // 处理，view更新的事件，转译一下AGGrid的接口
  const handleUpdateViewFieldsWidth = useCallback(
    async (width: number, colId: string, isReload = true) => {
      const list = tempFieldList != null ? tempFieldList : fieldWidths;

      const newFieldsWidth = produce(list, (draft) => {
        const foundField = draft.find((field) => field.id === colId);
        if (foundField) {
          foundField.width = width;
        } else {
          draft.push({
            id: colId,
            width,
          });
        }
      });

      const filterRSValue: RemoteStoragePropertyDatabaseViewColWidth = {
        type: 'DATABASE_VIEW_COL_WIDTH',
        viewId,
        fields: newFieldsWidth.filter((item) => Boolean(item.width)),
      };
      setFieldsWidth(filterRSValue);

      if (!(permission && permission.privilege.privilege === 'FULL_ACCESS')) return;

      if (!viewBo) {
        return;
      }
      const databaseFields = viewBo?.fields ?? [];
      let newFieldList: ViewField[];
      if (tempFieldList != null) {
        newFieldList = produce(tempFieldList, (draft) => {
          const viewItemIndex = draft?.findIndex((item) => item.id === colId);
          if (viewItemIndex > -1) {
            draft[viewItemIndex].width = width;
          }
        });
      } else {
        newFieldList = produce(databaseFields, (draft) => {
          const viewItemIndex = draft?.findIndex((item) => item.id === colId);
          if (viewItemIndex > -1) {
            draft[viewItemIndex].width = width;
          }
        });
      }

      if (isEqual(viewBo.fields, newFieldList)) {
        return;
      }

      await api.updateView(
        {
          databaseId,
          viewId,
          data: {
            ...viewBo,
            fields: newFieldList,
          },
        },
        isReload,
      );

      setTempFieldList(newFieldList);
      if (isReload) {
        await utils.node.boInfo.invalidate();
      }
    },
    [props, viewBo, viewId, databaseId, permission, tempFieldList, fieldWidths],
  );

  const handleMenuClick = useCallback(
    async (evt: BGridMenuEvent) => {
      if (evt.type === 'batchUpdate') {
        const data = evt.data;
        const { recordIds } = data;
        spaceContext.showUIDrawer({
          type: 'table-bulk-editor',
          props: {
            databaseId,
            viewId,
            recordIds,
          },
        });
      }

      if (evt.type === 'copyRow') {
        const data = evt.data;
        const newRecordDetail = getRecordDTOFromRecordDetailVO(
          {
            revision: 0,
            fields: columns,
            record: data,
          },
          false,
          locale,
        );
        await createRecord({
          ...newRecordDetail,
          databaseId: data.databaseId,
          mirrorId: props.mirrorId,
        }).then(() => {
          toast(t.copy.copy_success, {
            variant: 'success',
          });
          const agApi = bGridRef?.current?.getApi?.();
          agApi?.refreshServerSide();
        });
      }
    },
    [spaceContext, columns, createRecord, toast],
  );

  // fix: remote storage需要user验证, 分享表情况下不能卡住loading状态
  // if (isHiddenColumnLoading || isViewSortDataLoading || isViewGroupDataLoading) {
  //   return <Skeleton pos="NODE_PAGE" />;
  // }
  const handleRefresh = useCallback(() => {
    const agApi = bGridRef?.current?.getApi?.();
    agApi?.refreshServerSide({ purge: true });
  }, []);

  // Expose imperative methods through ref
  useImperativeHandle(
    ref,
    () => ({
      refresh: (filterInfo?: ViewFilter, kw?: string) => {
        if (filterInfo !== undefined && kw !== undefined) {
          refreshGrid(filterInfo, kw);
        } else {
          handleRefresh();
        }
      },
      getApi: () => bGridRef?.current?.getApi?.(),
    }),
    [handleRefresh, refreshGrid],
  );

  if (!view) {
    // TODO add skeleton
    return <Skeleton pos="NODE_PAGE" />;
  }

  return (
    <>
      {!props.hideToolbar && (
        <ViewHeaderToolbar
          handleRefresh={handleRefresh}
          view={view}
          editable={!disableEditing}
          locale={locale}
          databaseId={databaseId}
          mirrorId={mirrorId}
          setKeyword={setSearchKeyword}
          toolbarControl={props.toolbarControl}
          isTemplatePreview={props.isTemplatePreview}
          beforeLeftToolbar={props.beforeLeftToolbar}
        />
      )}

      <BGrid
        ref={bGridRef}
        pagination={pagination}
        className={className}
        onMenuClick={handleMenuClick}
        onUpdateCustomFields={setHiddenColumns}
        rowGroup={storageRowGroupOrTemplateRowGroup}
        hasSequenceColumn={props.hasSequenceColumn}
        tipSharingLogin={tipSharingLogin}
        disableEditing={disableEditing}
        handleUpdateViewFieldsWidth={handleUpdateViewFieldsWidth}
        disableOperateColumn={props.disableOperateColumn}
        onPaging={handlePage}
        sorts={theSort}
        onFirstDataRendered={onFirstDataRendered}
        onPaginationChanged={handlePaginationChanged}
        cachedBlockSize={cachedBlockSize}
        pageSize={pageSize}
        setPageSize={setPageSize}
        tempFieldList={tempFieldList}
        page={page}
        spaceId={spaceId}
        databaseId={databaseId}
        view={combinedView}
        mirrorId={mirrorId}
        onSort={handleSort}
        searchKeywords={searchKeyword}
        filter={filter}
        viewSort={viewSort}
        customViewFields={checkedCustomFieldViews}
        customFieldList={fieldWidths}
        handleUpdateFieldViewsSeq={handleUpdateViewFieldsSeq}
        rowHeight={rowHeight}
        isTemplatePreview={props.isTemplatePreview}
      />
    </>
  );
});

TableViewRendererBase.displayName = 'TableViewRendererBase';

export const TableViewRenderer = memo(TableViewRendererBase);
