import type { ViewVO } from '@bika/types/database/vo';
import type { ReactNode } from 'react';

export interface ICustomToolbar {
  // 左边toolbar的前面放东西
  beforeLeftToolbar?: (view: ViewVO) => ReactNode;

  // 是否不显示原来的toolbar？通常用于CrudDatabaseManager
  disableDefaultToolbar?: boolean;

  afterLeftToolbar?: ReactNode;

  beforeRightToolbar?: ReactNode;
  afterRightToolbar?: ReactNode;

  hideToolbar?: boolean;

  toolbarControl?: {
    hiddenAddRecord?: boolean;
    hiddenRequestRecord?: boolean;
    hiddenBulkUpdate?: boolean;
    hiddenSearch?: boolean;
    hiddenFilter?: boolean;
    hiddenSort?: boolean;
    hiddenHiddenColumn?: boolean;
    hiddenRowGroup?: boolean;
    hiddenDivider?: boolean;
  };
}
