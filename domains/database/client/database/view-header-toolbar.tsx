import type { ILocaleContext } from '@bika/contents/i18n';
import { VisibilityAdapter } from '@bika/domains/shared/client/components/visibility-adapter';
import { useRemoteStorage } from '@bika/domains/system/client/hooks/useRemoteStorage';
import type { View<PERSON>ield, ViewFilter, ViewSortArray } from '@bika/types/database/bo';
import { useDatabaseVOContext } from '@bika/types/database/context';
import type { FieldVO, ViewVO } from '@bika/types/database/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import type {
  RemoteStoragePropertyDatabaseViewFilter,
  RemoteStoragePropertyDatabaseViewHiddenColumn,
  RemoteStoragePropertyDatabaseViewRowGroup,
  RemoteStoragePropertyDatabaseViewRowHeight,
  RemoteStoragePropertyDatabaseViewSort,
} from '@bika/types/system/remote-storage';
import { type RowHeight, RowHeightEnum } from '@bika/types/system/remote-storage';
import { Input } from '@bika/ui/forms';
import { useUIFrameworkContext } from '@bika/ui/framework/context';
import AddCircleOutlined from '@bika/ui/icons/components/add_circle_outlined';
import RefreshOutlined from '@bika/ui/icons/components/refresh_outlined';
import SearchOutlined from '@bika/ui/icons/components/search_outlined';
import TransferOutlined from '@bika/ui/icons/components/transfer_outlined';
import TaskListOutlined from '@bika/ui/icons/doc_hide_components/task_list_outlined';
import { Divider, Stack } from '@bika/ui/layouts';
import { useSnackBar } from '@bika/ui/snackbar';
import { styled } from '@bika/ui/styled';
import { Typography } from '@bika/ui/text-components';
import { TooltipButton } from '@bika/ui/tooltip';
import { debounce, isEqual } from 'lodash';
import React, { type ChangeEvent, useCallback, useEffect, useMemo } from 'react';
import { Filter } from '../control-bar/toolbar/filter';
import { HiddenColumnView } from '../control-bar/toolbar/hidden-column-view';
import { RowGroupButton } from '../control-bar/toolbar/row-group-button';
import {
  useResponsiveDisplayManager,
  useResponsiveDisplayStyles,
} from '../control-bar/toolbar/use-responsive-display-hook';
import { ViewSortButton } from '../control-bar/toolbar/view-sort';
import type { ICustomToolbar } from './interface';
import { useCalucatedFields } from './useCalucatedFields';
import LineHeightButton from './view-header-line-height-button';

export { getRowHeightIcon } from './view-header-line-height-button';

export const useRowHeight = (viewId: string) => {
  const [rowHeightData, setRemoteRowHeight] =
    useRemoteStorage<RemoteStoragePropertyDatabaseViewRowHeight>(
      'DATABASE_VIEW_ROW_HEIGHT',
      viewId,
    );

  const rowHeight = rowHeightData?.height || RowHeightEnum.NARROW;

  const setRowHeight = useCallback(
    (height: RowHeight) => {
      if (height === rowHeight) {
        return;
      }

      const rowHeightValue: RemoteStoragePropertyDatabaseViewRowHeight = {
        type: 'DATABASE_VIEW_ROW_HEIGHT',
        viewId,
        height,
      };
      setRemoteRowHeight(rowHeightValue);
    },
    [setRemoteRowHeight, viewId, rowHeight],
  );

  return { rowHeight, setRowHeight };
};

export const StyledTooltipButton = styled(TooltipButton)`
  padding-left: 8px;
  padding-right: 8px;
  padding-top: 4px;
  padding-bottom: 4px;

  & > .MuiButton-startDecorator {
    margin-right: 0;
  }

  & > .MuiButton-startDecorator + p {
    margin-left: 8px;
  }
`;

interface Props extends ICustomToolbar {
  view: ViewVO;
  locale: ILocaleContext;
  editable?: boolean;
  databaseId: string;
  mirrorId?: string;
  setKeyword?: (keyword: string) => void;
  bulkRecordIds?: string[];
  kanbanGroupButton?: React.ReactNode;
  isTemplatePreview?: boolean;
  handleRefresh?: () => void;
}

export const ViewHeaderToolbar = (props: Props) => {
  // const colors = useCssColor();
  const spaceContext = useSpaceContextForce();
  const api = useDatabaseVOContext();
  const {
    view,
    databaseId,
    mirrorId,
    locale,
    editable = true,
    setKeyword,
    isTemplatePreview,
    handleRefresh,
  } = props;

  const { t } = locale;

  // Use custom hook to get responsive display styles with dynamic media query
  const { responsiveDisplayStyles } = useResponsiveDisplayStyles();

  useResponsiveDisplayManager();

  const viewId = view.id;

  const [disableRowGroup, setDisableRowGroup] = React.useState(false);

  // const [globalAGGridApi] = useGlobalState<GridApi<RecordRenderVO>>('AG_GRID_API');

  const { rowHeight, setRowHeight } = useRowHeight(mirrorId ?? viewId);

  // const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  // const open = Boolean(anchorEl);

  const { toast } = useSnackBar();

  // const handleClick = (event: React.MouseEvent<HTMLElement>) => {
  //   setAnchorEl(event.currentTarget);
  // };

  // const handleClose = () => {
  //   setAnchorEl(null);
  // };

  const handleRowHeightChange = (height: RowHeight) => {
    setRowHeight(height);
    // handleClose();
  };

  const { searchParams } = useUIFrameworkContext();
  //
  const searchQueryText = useMemo(() => searchParams.get('search') ?? '', [searchParams]);

  const [searchKeyword, setSearchKeywordState] = React.useState<string>(
    searchQueryText ? decodeURIComponent(searchQueryText) : '',
  );

  const setSearchKeywordFunction = useCallback(
    (value: string) => {
      setKeyword?.(value);
      if (value) {
        searchParams.set('search', encodeURIComponent(value));
      } else {
        searchParams.set('search', '');
      }
    },
    [searchParams, setKeyword],
  );

  const setSearchKeyword = debounce(setSearchKeywordFunction, 300);

  // 个人用户的filter信息，从远程获取
  const [filterData, setFilterData] = useRemoteStorage<RemoteStoragePropertyDatabaseViewFilter>(
    'DATABASE_VIEW_FILTER',
    mirrorId ?? viewId,
  );
  const [viewSortData, setRemoteViewSort] = useRemoteStorage<RemoteStoragePropertyDatabaseViewSort>(
    'DATABASE_VIEW_SORT',
    mirrorId ?? viewId,
  );

  // 隐藏列维护,从远程获取
  const [hiddenColumnsData, setHiddenColumnsData] =
    useRemoteStorage<RemoteStoragePropertyDatabaseViewHiddenColumn>(
      'DATABASE_VIEW_HIDDEN_COLUMNS',
      mirrorId ?? viewId,
    );

  const [rowGroupData, setRemoteRowGroup] =
    useRemoteStorage<RemoteStoragePropertyDatabaseViewRowGroup>(
      'DATABASE_VIEW_ROW_GROUP',
      mirrorId ?? viewId,
    );

  const viewRowGroup = rowGroupData?.data;

  const setRowGroup = useCallback(
    (groups: ViewSortArray) => {
      if (isEqual(groups, viewRowGroup)) {
        return;
      }

      const filterRSValue: RemoteStoragePropertyDatabaseViewRowGroup = {
        type: 'DATABASE_VIEW_ROW_GROUP',
        viewId,
        data: groups,
      };
      setRemoteRowGroup(filterRSValue);
    },
    [setRemoteRowGroup, viewId, viewRowGroup],
  );

  useEffect(() => {
    const isGalleryViewWithGroups =
      view.type === 'GALLERY' && view.groups && view.groups.length > 0;

    if (!isGalleryViewWithGroups) {
      setDisableRowGroup(false);
      return;
    }

    setDisableRowGroup(true);

    if (viewRowGroup && view.groups && !isEqual(view.groups, viewRowGroup)) {
      setRowGroup(view.groups);
    }
  }, [view.type, view.groups, viewRowGroup, setRowGroup]);

  const viewSort = viewSortData?.sort;
  const viewFilter = filterData?.filters;

  const setFilter = useCallback(
    (filter: ViewFilter) => {
      const filterRSValue: RemoteStoragePropertyDatabaseViewFilter = {
        type: 'DATABASE_VIEW_FILTER',
        viewId: mirrorId ?? viewId,
        filters: filter,
      };
      setFilterData(filterRSValue);
    },
    [setFilterData, viewId, mirrorId],
  );
  const setViewSort = useCallback(
    (_sort: ViewSortArray) => {
      const filterRSValue: RemoteStoragePropertyDatabaseViewSort = {
        type: 'DATABASE_VIEW_SORT',
        viewId: mirrorId ?? viewId,
        sort: _sort,
      };
      setRemoteViewSort(filterRSValue);
    },
    [setRemoteViewSort, viewId, mirrorId],
  );

  const setHiddenColumns = useCallback(
    (fields: ViewField[]) => {
      const rsData: RemoteStoragePropertyDatabaseViewHiddenColumn = {
        type: 'DATABASE_VIEW_HIDDEN_COLUMNS',
        viewId: mirrorId ?? viewId,
        fields,
      };
      setHiddenColumnsData(rsData);
    },
    [setHiddenColumnsData, viewId, mirrorId],
  );

  function onClickShowCreateRecordModal() {
    if (api.onClickCreate) {
      api.onClickCreate(databaseId, viewId, mirrorId);
    } else {
      spaceContext.showUIModal({
        type: 'create-record',
        databaseId,
        viewId,
        mirrorId,
        disabledBackdropClick: true,
      });
    }
  }

  function onClickShowRequestCreateRecordModal() {
    spaceContext.showUIModal({ type: 'request-record-mission', databaseId, viewId, mirrorId });
  }

  const hiddenColumns = useMemo(() => {
    const columns = view.columns
      .filter((col) => !col.hidden)
      .map((field) => ({ ...field, hidden: false }));
    if (!hiddenColumnsData) {
      return columns;
    }
    return columns.map((field) => {
      const hiddenColumn = hiddenColumnsData?.fields.find((column) => column.id === field?.id);
      return {
        ...field,
        hidden: hiddenColumn?.hidden || false,
      };
    });
  }, [view.columns, hiddenColumnsData]);

  const newFieldList = useCalucatedFields(view.columns, databaseId);

  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      sx={{
        py: '4px',
        overflowX: 'hidden',
        overflowY: 'hidden',
        maxWidth: '100%',
        height: '40px',
      }}
    >
      <Stack spacing={1} direction="row" my={1} className="databaseToolbarContainer">
        <StyledTooltipButton
          tooltipTitle={t.editor.button_refresh}
          color="neutral"
          sx={{
            color: 'var(--text-primary)',
            textWrap: 'nowrap',
            fontWeight: 400,
            '&:hover': {
              backgroundColor: 'var(--hover)',
            },
          }}
          onClick={() => {
            handleRefresh?.();

            toast(t.wizard.already_freshed, {
              variant: 'success',
            });
          }}
          variant="plain"
          startDecorator={<RefreshOutlined size={16} color="var(--text-secondary)" />}
        >
          <Typography
            level="b4"
            sx={{
              textWrap: 'nowrap',
              ...responsiveDisplayStyles,
            }}
          >
            {t.editor.button_refresh}
          </Typography>
        </StyledTooltipButton>

        {props.beforeLeftToolbar?.(view)}

        {props.disableDefaultToolbar !== true && (
          <>
            {props.toolbarControl?.hiddenAddRecord !== true && (
              <VisibilityAdapter privilege="CAN_EDIT" disabledWithinShared>
                <StyledTooltipButton
                  tooltipTitle={t.grid.new_record}
                  disabledTooltipTitle={t.editor.table_lock_message}
                  color="neutral"
                  disabled={!editable}
                  sx={{
                    color: 'var(--text-primary)',
                    textWrap: 'nowrap',
                    fontWeight: 400,
                    '&:hover': {
                      backgroundColor: 'var(--hover)',
                    },
                  }}
                  onClick={onClickShowCreateRecordModal}
                  variant="plain"
                  startDecorator={<AddCircleOutlined size={16} color="var(--text-secondary)" />}
                >
                  <Typography
                    level="b4"
                    sx={{
                      textWrap: 'nowrap',
                      ...responsiveDisplayStyles,
                    }}
                  >
                    {t.grid.new_record}
                  </Typography>
                </StyledTooltipButton>
              </VisibilityAdapter>
            )}

            {/* 请求数据按钮 */}
            {props.toolbarControl?.hiddenRequestRecord !== true && (
              <VisibilityAdapter privilege="FULL_ACCESS">
                <StyledTooltipButton
                  disabledTooltipTitle={t.editor.table_lock_message}
                  tooltipTitle={t.record.request_new_record}
                  disabled={!editable}
                  color="neutral"
                  sx={{
                    color: 'var(--text-primary)',
                    fontWeight: 400,
                    '&:hover': {
                      backgroundColor: 'var(--hover)',
                    },
                  }}
                  onClick={onClickShowRequestCreateRecordModal}
                  variant="plain"
                  startDecorator={<TransferOutlined size={16} color="var(--text-secondary)" />}
                >
                  <Typography
                    level="b4"
                    sx={{
                      textWrap: 'nowrap',
                      ...responsiveDisplayStyles,
                    }}
                  >
                    {t.record.request_new_record}
                  </Typography>
                </StyledTooltipButton>
              </VisibilityAdapter>
            )}

            {!props.toolbarControl?.hiddenDivider && (
              <Divider
                sx={{ '--Divider-childPosition': '80%', height: '16px', top: '8px' }}
                orientation="vertical"
              />
            )}

            <VisibilityAdapter privilege="CAN_VIEW">
              <>
                {props.toolbarControl?.hiddenHiddenColumn !== true && (
                  <HiddenColumnView
                    disabled={isTemplatePreview}
                    values={hiddenColumns}
                    enabled={hiddenColumns.some((col) => col.hidden === true)}
                    onChange={async (newValue) => {
                      setHiddenColumns(newValue);
                    }}
                  />
                )}

                {view.type === 'KANBAN' && props?.kanbanGroupButton}

                <Filter
                  disabled={isTemplatePreview}
                  locale={locale}
                  value={viewFilter}
                  width={'720px'}
                  maxHeight={280}
                  fieldList={newFieldList}
                  onChange={(v: ViewFilter) => {
                    setFilter(v);
                  }}
                />

                {view.type !== 'KANBAN' && props.toolbarControl?.hiddenRowGroup !== true && (
                  <RowGroupButton
                    disabled={disableRowGroup || isTemplatePreview}
                    fieldList={(view.columns ?? []).filter((item) => item.hidden !== true)}
                    value={viewRowGroup}
                    onChange={(v) => {
                      if (v) {
                        setRowGroup(v);
                      }
                    }}
                    viewType={view.type}
                  />
                )}

                <ViewSortButton
                  disabled={isTemplatePreview}
                  fieldList={newFieldList as unknown as FieldVO[]}
                  value={viewSort}
                  onChange={(v) => {
                    setViewSort(v);
                  }}
                />

                {/* Row Height Button */}
                {view.type === 'TABLE' && (
                  <LineHeightButton
                    rowHeight={rowHeight}
                    onRowHeightChange={handleRowHeightChange}
                  />
                )}
              </>
            </VisibilityAdapter>

            {props.toolbarControl?.hiddenBulkUpdate !== true && (
              <VisibilityAdapter privilege="CAN_EDIT_CONTENT">
                <StyledTooltipButton
                  tooltipTitle={t.grid.bulk_update}
                  disabledTooltipTitle={t.editor.table_lock_message}
                  disabled={!editable}
                  onClick={() => {
                    spaceContext.showUIDrawer({
                      type: 'table-bulk-editor',
                      props: {
                        recordIds: props.bulkRecordIds,
                        databaseId,
                        viewId,
                      },
                    });
                  }}
                  color={'neutral'}
                  sx={{
                    color: 'var(--text-primary)',
                    textWrap: 'nowrap',
                    fontWeight: 400,
                    '&:hover': {
                      backgroundColor: 'var(--hover)',
                    },
                  }}
                  variant={'plain'}
                  startDecorator={<TaskListOutlined size={16} color="var(--text-secondary)" />}
                >
                  <Typography
                    level="b4"
                    sx={{
                      textWrap: 'nowrap',
                      ...responsiveDisplayStyles,
                    }}
                  >
                    {t.grid.bulk_update}
                  </Typography>
                </StyledTooltipButton>
              </VisibilityAdapter>
            )}
          </>
        )}

        {props.afterLeftToolbar}
      </Stack>

      {/* TODO use searchInput instead */}
      {!isTemplatePreview && (
        <Input
          placeholder={t.filter.search}
          startDecorator={<SearchOutlined color="var(--text-secondary)" />}
          size="md"
          value={searchKeyword}
          sx={{
            height: '32px',
            width: '120px',
            minWidth: '120px',
            backgroundColor: view.type === 'KANBAN' ? 'var(--bg-controls)' : 'var(--bg-surface)',
            borderRadius: '20px',
          }}
          onInput={(e: ChangeEvent<HTMLInputElement>) => {
            setSearchKeywordState(e.target.value);
            // debounced
            setSearchKeyword?.(e.target.value);
          }}
          onBlur={(e: ChangeEvent<HTMLInputElement>) => {
            setSearchKeyword(e.target.value);
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              setSearchKeyword?.(searchKeyword);
            }
          }}
        />
      )}
    </Stack>
  );
};
