import { AutomationEventsReceiver } from './events-receiver/automation-events-receiver';
import { DatabaseEventsReceiver } from './events-receiver/database-events-receiver';
import { FolderEventsReceiver } from './events-receiver/folder-events-receiver';
import { MissionEventsReceiver } from './events-receiver/mission-events-receiver';
import { NodeEventsReceiver } from './events-receiver/node-events-receiver';
import { ReportEventsReceiver } from './events-receiver/report-events-receiver';
import { SpaceEventsReceiver } from './events-receiver/space-events-receiver';
import { SurveysEventsReceiver } from './events-receiver/survey-events-receiver';
import { UserEventsReceiver } from './events-receiver/user-events-receiver';

/**
 * System Server Event总线模块，除了是事件绑定，还是一些特殊的、业务化、零散的功能相关的逻辑，不想侵入到其它SO中，放到这里
 * 比如，新建空间站就会创建默认Mission
 */
export class EventSO {
  private static _mission: MissionEventsReceiver = new MissionEventsReceiver();

  static get mission() {
    return EventSO._mission;
  }

  private static _survey: SurveysEventsReceiver = new SurveysEventsReceiver();

  static get survey() {
    return EventSO._survey;
  }

  private static _report: ReportEventsReceiver = new ReportEventsReceiver();

  static get report() {
    return EventSO._report;
  }

  private static _database: DatabaseEventsReceiver = new DatabaseEventsReceiver();

  static get database() {
    return EventSO._database;
  }

  private static _automation: AutomationEventsReceiver = new AutomationEventsReceiver();

  static get automation() {
    return EventSO._automation;
  }

  private static _space: SpaceEventsReceiver = new SpaceEventsReceiver();

  static get space() {
    return EventSO._space;
  }

  private static _user: UserEventsReceiver = new UserEventsReceiver();

  static get user() {
    return EventSO._user;
  }

  private static _folder: FolderEventsReceiver = new FolderEventsReceiver();

  static get folder() {
    return EventSO._folder;
  }

  private static _node: NodeEventsReceiver = new NodeEventsReceiver();

  static get node() {
    return EventSO._node;
  }
}
