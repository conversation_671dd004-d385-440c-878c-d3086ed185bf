import { getTranslationsByLocale } from '@bika/contents/i18n/translate';
import { AutomationListener } from '@bika/domains/automation/server/listener/automation-listener';
import { TriggerListener } from '@bika/domains/automation/server/listener/trigger-listener';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CommentSO } from '@bika/domains/database/server';
import type { DatabaseSO } from '@bika/domains/database/server/database-so';
import type { FieldSO } from '@bika/domains/database/server/fields/field-so';
import { RecordSO } from '@bika/domains/database/server/record-so';
import { MissionSO } from '@bika/domains/mission/server/mission-so';
import { NotificationSO } from '@bika/domains/notification/server/notification-so';
import { SpaceMySO } from '@bika/domains/space/server/space-my-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db } from '@bika/server-orm';
import type { CellValue, DatabaseFieldWithId } from '@bika/types/database/bo';
import { isInCI } from 'sharelib/app-env';
import { AISearchSO } from '../../../../ai/server/ai-search-so';
import { ChangeLogFactory } from '../../../../change/server/change-log-factory';
import { OnlineSessionSO } from '../../../../system/server/online-session-so';
import { OutgoingListener } from '../../listener/outgoing-listener';
import { SseSO } from '../../sse/sse-so';
import type { RecordCreatedOptions, RecordUpdatedEventOption } from './types';

export class DatabaseEventsReceiver {
  async onDatabaseCreated(databaseSO: DatabaseSO) {
    // const records = await databaseSO.getRecordsCount();

    const promises: Promise<unknown>[] = [];

    if (databaseSO.model.createdBy) {
      // sse
      promises.push(
        SpaceMySO.emitSseSpaceSidebar(databaseSO.model.createdBy, databaseSO.spaceId, [
          'ROOT_NODE',
        ]),
      );
    }
    // 默认记录
    // if (records > 0) {
    //   promises.push(this.addRecordUsageLog(databaseSO.id, records));
    // }

    await Promise.allSettled(promises);
  }

  async onFieldUpdated(database: DatabaseSO, fromField: FieldSO, toField: FieldSO) {
    TriggerListener.onFieldUpdatedEvent(database, fromField, toField);
  }

  async onFieldDeleted(database: DatabaseSO, field: FieldSO) {
    TriggerListener.onFieldDeletedEvent(database, field);
  }

  async onRecordUpdated(
    currentRecord: RecordSO,
    modifiedFieldData: Record<
      string,
      { field: DatabaseFieldWithId; from: CellValue; to: CellValue }
    >,
    options?: RecordUpdatedEventOption,
  ) {
    const { user, previousField, previousRecordModel } = options ?? {};

    const promises: Promise<unknown>[] = [
      // automation
      AutomationListener.onRecordUpdatedEvent({
        database: currentRecord.database,
        record: currentRecord,
        modifiedFieldData,
        userId: user?.id,
      }),
      AISearchSO.writeDatabaseRecords(currentRecord.database.toNodeSO(), [currentRecord]),
    ];

    // record change log
    if (previousRecordModel) {
      promises.push(
        // record change log
        ChangeLogFactory.write(
          'DATABASE_RECORD',
          { previous: previousRecordModel, current: currentRecord.model },
          {
            user,
            database: currentRecord.database,
            previousFields: previousField ? [previousField] : undefined,
          },
        ),
      );
    }

    if (user) {
      // mission
      const missionFunc = async () => {
        const member = await user.getMember(currentRecord.spaceId);
        await MissionSO.resolve(member, { type: 'UPDATE_RECORD', recordId: currentRecord.id });
      };
      promises.push(missionFunc());
    }

    // sse
    const databaseId = currentRecord.databaseId;
    const spaceId = currentRecord.spaceId;
    const sseFunc = async () => {
      const onlineSessions = await OnlineSessionSO.getByRoute(
        `/space/${spaceId}/node/${databaseId}`,
      ); // 只要url里带这个id就会匹配上
      for (const item of onlineSessions) {
        // 注意这里，如果"我"打开了多个窗口，是收不到刷新提醒的 TODO: 是否自己也要提醒？
        if (item.userId !== user?.id) {
          const spaceMember = await UserSO.init(item.userId);
          const t = await getTranslationsByLocale(spaceMember.locale);

          SseSO.emit(item.userId, {
            name: 'snackbar',
            text: t.resource.content_changed_warning,
            closeable: false,
          });
        }
      }
    };
    promises.push(sseFunc());

    await Promise.allSettled(promises);
  }

  /**
   * 批量记录变更事件
   */
  async onRecordsUpdated(
    user: UserSO | null,
    database: DatabaseSO,
    batchRecordChange: BatchRecordChange,
  ) {
    // 批量记录变更事件
    const { changes, previousFields, previousRecords } = batchRecordChange;
    // 变更的记录ID列表
    const updateRecordIds = changes.map((change) => change.recordId);
    if (updateRecordIds.length === 0) {
      return; // 如果没有记录变更，直接返回
    }
    // 找到最新的记录数据列表
    const records = await database.getRecords(updateRecordIds);

    // 所有IO操作
    const promises: Promise<unknown>[] = [];
    // 自动化事件
    for (const record of records) {
      const recordChange = changes.find((change) => change.recordId === record.id);
      if (!recordChange) {
        continue; // 如果没有变更，跳过
      }
      // 变更的字段数据
      const modifiedFieldData = recordChange.cellChanges.reduce<
        Record<string, { field: DatabaseFieldWithId; from: CellValue; to: CellValue }>
      >((acc, { field, from, to }) => {
        acc[field.id] = { field, from, to };
        return acc;
      }, {});
      promises.push(
        AutomationListener.onRecordUpdatedEvent({
          database,
          record,
          modifiedFieldData,
          userId: user?.id,
        }),
      );
    }

    // 记录同步
    promises.push(AISearchSO.writeDatabaseRecords(database.toNodeSO(), records));

    // 记录变更日志事件
    promises.push(
      ChangeLogFactory.batchWrite(
        'DATABASE_RECORD',
        records.map((record) => {
          const recordChange = changes.find((change) => change.recordId === record.id);
          if (!recordChange) {
            return { current: record.model }; // 如果没有变更，直接返回当前记录
          }
          return { current: record.model, previous: previousRecords[record.id] };
        }),
        { user: user ?? undefined, database, previousFields },
      ),
    );

    // 任务事件
    if (user) {
      const member = await user.getMember(database.spaceId);
      const calMissions = records.map(async (record) =>
        MissionSO.resolve(member, { type: 'UPDATE_RECORD', recordId: record.id }),
      );
      promises.push(...calMissions);
    }

    // SSE事件
    const sseFunc = async () => {
      const onlineSessions = await OnlineSessionSO.getByRoute(
        `/space/${database.spaceId}/node/${database.id}`,
      ); // 只要url里带这个id就会匹配上
      for (const item of onlineSessions) {
        // 注意这里，如果"我"打开了多个窗口，是收不到刷新提醒的 TODO: 是否自己也要提醒？
        if (item.userId !== user?.id) {
          const spaceMember = await UserSO.init(item.userId);
          const t = await getTranslationsByLocale(spaceMember.locale);

          SseSO.emit(item.userId, {
            name: 'snackbar',
            text: t.resource.content_changed_warning,
            closeable: false,
          });
        }
      }
    };
    promises.push(sseFunc());

    // 一起执行
    await Promise.allSettled(promises);
  }

  async onRecordDeleted(recordSO: RecordSO) {
    await this.onRecordsDeleted(recordSO.database, [recordSO]);
  }

  async onRecordsDeleted(database: DatabaseSO, records: RecordSO[]) {
    const promises: Promise<unknown>[] = [
      // trigger record event
      TriggerListener.onRecordDeletedEvent(database, records),
      // 更新记录数
      database.updateNodeState(),
      // 更新记录用量
      // this.addRecordUsageLog(database.id, -records.length),
    ];
    records.forEach((recordSO) => {
      promises.push(db.search.delete(recordSO.id, 'RESOURCE_CONTENT', 'exact'));
    });

    await Promise.allSettled(promises);
  }

  async onRecordCreated(database: DatabaseSO, records: RecordSO[], options?: RecordCreatedOptions) {
    const { member, formId } = options ?? {};

    const promises: Promise<unknown>[] = [
      // automation
      AutomationListener.onRecordCreatedEvent({
        database,
        records,
        formId,
        userId: member?.userId,
      }),
      // 更新记录数
      database.updateNodeState(),

      // 添加记录用量
      // this.addRecordUsageLog(database.id, records.length),

      // record subscription

      // ...
    ];

    // es index
    promises.push(AISearchSO.writeDatabaseRecords(database.toNodeSO(), records));

    if (member) {
      const recordIds = records.map((recordSO) => recordSO.id);
      // mission。当一行数据被创建后，如果用户有对应的这个数据库的Mission，就完成它
      promises.push(
        MissionSO.resolve(member, {
          type: 'CREATE_RECORD',
          databaseId: database.id,
          recordId: recordIds[0],
        }),
      );
      promises.push(
        MissionSO.resolve(member, {
          type: 'CREATE_MULTI_RECORDS',
          databaseId: database.id,
          recordIds,
        }),
      );
      // sse
      promises.push(SpaceMySO.emitSseSpaceSidebar(member.userId, database.spaceId, ['ROOT_NODE']));
    }

    // record change log
    promises.push(
      ChangeLogFactory.batchWrite(
        'DATABASE_RECORD',
        records.map((record) => ({ current: record.model })),
        { user: member ? await member.getUser() : undefined, database },
      ),
    );

    await Promise.allSettled(promises);
    // not await to avoid blocking
    if (isInCI()) {
      await OutgoingListener.onRecordCreated(database, records, { memberSO: member, formId });
    } else {
      OutgoingListener.onRecordCreated(database, records, { memberSO: member, formId });
    }
  }

  // private async addRecordUsageLog(databaseId: string, value: number) {
  //   await EventSO.usageLog.triggerUsageLogCreated('spaceId', {
  //     type: 'RECORDS_PER_SPACE',
  //     value,
  //     databaseId,
  //   });
  // }

  /**
   * 新评论创建后
   *
   * @param commentSO
   */
  async onRecordCommentCreated(commentSO: CommentSO) {
    const { id, relationId, unitId, content } = commentSO.model;
    const recordSO = await RecordSO.init(relationId, commentSO.model.spaceId);
    const fromMemberSO = await MemberSO.init(unitId!);

    Promise.allSettled([
      // mission
      MissionSO.resolve(fromMemberSO, {
        type: 'COMMENT_RECORD',
        recordId: recordSO.id,
        commentId: id,
      }),
      // notification
      this.commentNotification(id, content, recordSO, fromMemberSO),
    ]);
  }

  /**
   * 评论删除后
   *
   * @param commentSO
   */
  async onRecordCommentDeleted(commentSO: CommentSO) {
    // 暂时不需要特殊处理
  }

  /**
   * 通知被@的人。过滤掉自己
   */
  private async commentNotification(
    commentId: string,
    content: string,
    recordSO: RecordSO,
    fromMemberSO: MemberSO,
  ) {
    const { atMembers } = await this.parseCommentContent(content);
    atMembers
      .filter((toMemberSO) => toMemberSO.userId !== fromMemberSO.userId)
      .map(async (toMemberSO) => {
        NotificationSO.create(
          {
            type: 'DATABASE_RECORD_COMMENT_CREATED',
            databaseId: recordSO.databaseId,
            recordId: recordSO.id,
            commentId,
          },
          {
            type: 'USER',
            userId: toMemberSO.userId,
          },
        );
      });
  }

  /**
   * 从评论内容提取@的人和正文。
   *
   * @格式: @[name](member_id)
   */
  private async parseCommentContent(
    content: string,
  ): Promise<{ atMembers: MemberSO[]; text: string }> {
    const regex = /@\[(.*?)\]\((.*?)\)/g;
    const matches = [...content.matchAll(regex)];
    const atMembers = await Promise.all(matches.map(async (match) => MemberSO.init(match[2])));

    const text = content.replace(regex, (_, memberName, __) => `@${memberName}`);
    return { atMembers, text };
  }
}
