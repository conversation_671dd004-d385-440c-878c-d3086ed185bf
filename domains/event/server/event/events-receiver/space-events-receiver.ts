import { onSpaceCreatedMissions } from '@bika/contents/config/server';
import { getTranslationsByLocale } from '@bika/contents/i18n/translate';
import { MissionSO } from '@bika/domains/mission/server/mission-so';
import { SpaceMySO } from '@bika/domains/space/server/space-my-so';
import type { SpaceSO } from '@bika/domains/space/server/space-so';
import type { MemberSO } from '@bika/domains/unit/server/member-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import type { SpaceJoinInfo, SpaceUpdateInfo } from '@bika/types/space/vo';
import { SseSO } from '../../sse/sse-so';

export class SpaceEventsReceiver {
  /**
   * 空间创建完毕后，创建默认的Missions
   *
   * @param spaceSO
   */
  async onSpaceCreated(spaceSO: SpaceSO) {
    const mainAdminMember = await spaceSO.getOwner();
    // 初始化任务
    const promises: Promise<unknown>[] = onSpaceCreatedMissions.map((missionTpl) =>
      mainAdminMember.createMission(missionTpl),
    );
    // 记录席位用量日志
    // promises.push(UsageLogSO.create(spaceSO.id, { type: 'SEATS', value: 1 }));

    await Promise.allSettled(promises);
  }

  async onSpaceUpdated(spaceId: string, user: UserSO, updatedData: SpaceUpdateInfo) {
    const userExistOnSpace = await user.existSpace(spaceId);
    if (!userExistOnSpace) {
      // 有可能是站点管理更新, 不存在也正常
      return;
    }
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const { name } = updatedData;
    const mUser = await member.getUser();

    if (name) {
      await MissionSO.resolve(member, { type: 'SET_SPACE_NAME', spaceId });
      const t = await getTranslationsByLocale(mUser.locale);
      await SseSO.emit(member.userId, {
        name: 'snackbar',
        text: t('space.msg_space_name_modified_success', { spaceName: space.name }) as string,
        // action: {
        //   text: t.space.msg_go_to_space_settings,
        //   showModal: {
        //     type: 'space-settings',
        //     tab: { type: 'SPACE_SETTING' },
        //   },
        // },
      });
    }

    await SpaceMySO.emitSseSpaceSidebar(member.userId, spaceId, ['REDDOTS', 'ROOT_NODE']);
  }

  async onMemberJoined(member: MemberSO, joinInfo?: SpaceJoinInfo) {
    Promise.allSettled([
      // 已安装模板的新成员加入任务
      this.createTemplateNewMemberJoinMissions(member),
      // 分发成员邀请奖励
      this.distributeMemberInvitationReward(member, joinInfo),
      // 记录席位用量日志
      // UsageLogSO.create(member.spaceId, { type: 'SEATS', value: 1 }),
    ]);
  }

  private async createTemplateNewMemberJoinMissions(member: MemberSO) {
    const space = await member.getSpace();
    const memberVO = await member.toVO();
    const templateInstalls = await space.getTemplateInstallations();
    for (const templateInstall of templateInstalls) {
      const missions = templateInstall.template.newMemberJoinMissions;
      if (missions && missions.length > 0) {
        for (const mission of missions) {
          await MissionSO.createMission({
            spaceId: space.id,
            missionTemplate: mission,
            templateNodeId: templateInstall.referenceNodeId,
            props: { member: memberVO },
          });
        }
      }
    }
  }

  private async distributeMemberInvitationReward(member: MemberSO, joinInfo?: SpaceJoinInfo) {
    if (!joinInfo) {
      return;
    }
    const user = await member.getUser();
    const coinAccount = await user.coins.getAccount();

    // 判断用户是否是第一次加入，已经加入过的不再奖励
    const hasInvited = await coinAccount.existTransaction({
      reason: 'accept-member-invitation',
      spaceId: member.spaceId,
    });
    if (hasInvited) {
      return;
    }

    switch (joinInfo.joinType) {
      case 'LINK_INVITATION': {
        // 受邀成员奖励
        await coinAccount.earn(1000, 'CREDIT', {
          reason: 'accept-member-invitation',
          spaceId: member.spaceId,
          ...joinInfo,
        });

        // 邀请成员奖励
        const inviterUser = await UserSO.init(joinInfo.inviterUserId);
        const inviterUserCoinAccount = await inviterUser.coins.getAccount();
        await inviterUserCoinAccount.earn(1000, 'CREDIT', {
          reason: 'invite-member',
          spaceId: member.spaceId,
          joinType: joinInfo.joinType,
          invitedUserId: user.id,
        });
        break;
      }
      case 'EMAIL_INVITATION':
        break;
      default:
        break;
    }
  }

  async onSpaceInviteCreated(member: MemberSO, linkInvitationId: string, roleIds?: string[]) {
    await MissionSO.resolve(member, {
      type: 'INVITE_MEMBER',
      spaceId: member.spaceId,
      linkInvitationId,
      roleIds,
    });
  }
}
