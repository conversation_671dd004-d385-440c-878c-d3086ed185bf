import type { DatabaseSO } from '@bika/domains/database/server/database-so';
import type { FolderSO } from '@bika/domains/node/server/folder-so';
import type { NodeSO } from '@bika/domains/node/server/node-so';
import { SpaceMySO } from '@bika/domains/space/server/space-my-so';
import { TemplateRepoSO } from '@bika/domains/template/server/template-repo-so';
import type { UserSO } from '@bika/domains/user/server';
import { db } from '@bika/server-orm';
import { EventSO } from '../event-so';

export class FolderEventsReceiver {
  async onChildCreated(child: NodeSO) {
    const promises: Promise<unknown>[] = [child.updateState()];

    const handleChildCreated = async (node: NodeSO) => {
      promises.push(EventSO.node.onCreate(node));

      // if (!node.isFolder) {
      //   // 非文件夹类型都计量资源使用
      //   promises.push(
      //     EventSO.usageLog.triggerUsageLogCreated(child.spaceId, {
      //       type: 'RESOURCES',
      //       value: 1,
      //     }),
      //   );
      // }
      if (node.isDatabase) {
        const databaseSO = await node.toResourceSO<DatabaseSO>();
        promises.push(EventSO.database.onDatabaseCreated(databaseSO));
      }
      if (node.isFolder || node.isTemplate) {
        const folderSO = await node.toResourceSO<FolderSO>();
        const children = await folderSO.getChildren();
        for (const item of children) {
          await handleChildCreated(item);
        }
      }
    };
    await handleChildCreated(child);
    await Promise.allSettled(promises);
  }

  /**
   * when the delete a node.
   * @param nodeSO deleted child nodeSO
   */
  async onChildDeleted(user: UserSO, child: NodeSO, deletedNums: number) {
    const parent = await child.parentNode();
    if (parent) {
      // 递归更新父级节点的数量
      await parent.updateState();
    }

    SpaceMySO.emitSseSpaceSidebar(user.id, child.spaceId, ['ROOT_NODE']);
    db.search.delete(child.id, 'NODE_RESOURCE', { custom: child.spaceId });
    // await EventSO.usageLog.triggerUsageLogCreated(child.spaceId, {
    //   type: 'RESOURCES',
    //   value: deletedNums,
    // });
  }

  async onPublish(templateId: string) {
    const repoSO = await TemplateRepoSO.init(templateId);
    await db.search.write({
      id: templateId,
      indexData: {
        type: 'TEMPLATE_REPO',
        templateId,
        visibility: repoSO.visibility || 'PUBLIC',
        content: JSON.stringify(repoSO.repo, undefined, 2),
      },
    });
  }
}
