'use client';
import { useLocale } from '@bika/contents/i18n';
import { useSpaceContextForce } from '@bika/types/space/context';
import { useGlobalContext } from '@bika/types/website/context';
import { IconButton } from '@bika/ui/button';
import GenerateAiOutlined from '@bika/ui/icons/doc_hide_components/generate_ai_outlined';
import { Tooltip } from '@bika/ui/tooltip';
import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import type { INodeVOMenuProps } from './types';

export function NodeVOAICopilotMenuOriginal(props: Pick<INodeVOMenuProps, 'value'>) {
  const spaceContext = useSpaceContextForce();
  const searchParams = useSearchParams();
  const { t } = useLocale();
  const context = useGlobalContext();
  const adavancedAI = context.systemConfiguration.advancedAI;
  if (!adavancedAI) return null;

  const isCopilot = !!searchParams?.get('copilot');

  return (
    <Tooltip title={t.global.copilot.title}>
      <IconButton
        onClick={() => {
          if (isCopilot) {
            spaceContext.showAICopilot(null);
          } else {
            spaceContext.showAICopilot({
              type: 'node',
              nodeId: props.value.id,
            });
          }
        }}
        variant={isCopilot ? 'outlined' : 'plain'}
      >
        <GenerateAiOutlined
          color={isCopilot ? 'var(--brand) !important' : 'var(--text-secondary)'}
        />
      </IconButton>
    </Tooltip>
  );
}

export function NodeVOAICopilotMenu(props: Pick<INodeVOMenuProps, 'value'>) {
  return (
    <Suspense fallback={<></>}>
      <NodeVOAICopilotMenuOriginal {...props} />
    </Suspense>
  );
}
