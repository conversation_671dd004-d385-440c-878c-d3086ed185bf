import type { ILocaleContext } from '@bika/contents/i18n';
import { OnlineUsersView } from '@bika/domains/auth/client/online-users-view';
import { NodeVOAICopilotMenu } from '@bika/domains/editor/client/node-vo-menu/node-vo-ai-copilot-menu';
import { VisibilityAdapter } from '@bika/domains/shared/client/components/visibility-adapter';
import type { NodeDetailVO } from '@bika/types/node/vo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { Button } from '@bika/ui/button';
import { IconButton } from '@bika/ui/button-component';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import HistoryOutlined from '@bika/ui/icons/components/history_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { Tooltip } from '@bika/ui/tooltip';
import React from 'react';
import { NodeVOMenu } from '../../editor/client/node-vo-menu/node-vo-menu';

interface Props {
  nodeId: string;
  nodeDetail: NodeDetailVO;
  locale: ILocaleContext;
  isTemplatePreview?: boolean;
}
export function AutomationDetailViewTopRightButtons(props: Props) {
  const spaceContext = useSpaceContextForce();
  const { nodeId, nodeDetail, locale, isTemplatePreview } = props;
  const isManager = nodeDetail.permission?.privilege === 'FULL_ACCESS';
  const hasPermission = !!nodeDetail.permission?.privilege;

  const { t } = locale;

  return (
    <Stack direction="row" alignItems="center" spacing={1}>
      <OnlineUsersView />

      {hasPermission && (
        <VisibilityAdapter privilege="FULL_ACCESS">
          <Box id="AUTOMATION_EDIT_BUTTON">
            <Tooltip title={t.action.edit}>
              <IconButton
                variant="plain"
                color="neutral"
                onClick={() => {
                  spaceContext.showUIDrawer({
                    type: 'resource-editor',
                    props: {
                      screenType: 'NODE_RESOURCE',
                      resourceType: 'AUTOMATION',
                      nodeId,
                    },
                  });
                }}
              >
                <EditOutlined color="var(--text-secondary)" />
              </IconButton>
            </Tooltip>
          </Box>
        </VisibilityAdapter>
      )}
      <Tooltip title={t.automation.history}>
        <IconButton
          variant="plain"
          color="neutral"
          disabled={!isManager || isTemplatePreview}
          onClick={() => {
            spaceContext.showUIDrawer({
              type: 'automation-history',
              automationId: nodeId,
            });
          }}
        >
          <HistoryOutlined color="var(--text-secondary)" />
        </IconButton>
      </Tooltip>
      {!isTemplatePreview && <NodeVOAICopilotMenu value={nodeDetail} />}
      {hasPermission && <NodeVOMenu value={nodeDetail} detail={nodeDetail} />}
    </Stack>
  );
}
