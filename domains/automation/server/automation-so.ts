import { errors, ServerError } from '@bika/contents/config/server/error';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { type IRelationIdOpts, NodeResourceSO } from '@bika/domains/node/server/types';
import { getUnknownErrorMessage, Logger } from '@bika/domains/shared/server';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { SchedulerSO } from '@bika/domains/system/server/scheduler-so';
import {
  AutomationActionRelationType,
  AutomationTriggerRelationType,
} from '@bika/domains/system/server/types';
import { UserSO } from '@bika/domains/user/server/user-so';
import { $Enums, db, type Prisma, type PrismaPromise } from '@bika/server-orm';
import type { Action, Automation, Trigger } from '@bika/types/automation/bo';
import type { AutomationCreateBO, AutomationUpdateBO } from '@bika/types/automation/dto';
import type {
  ActionFakeResultVO,
  ActionRunDetailVO,
  AutomationRunHistoryStatus,
  AutomationVariablesVO,
  AutomationVO,
  TriggerRunDetailVO,
} from '@bika/types/automation/vo';
import type {
  ExportBOOptions,
  NodeResourceType,
  NodeStateBO,
  ToTemplateOptions,
} from '@bika/types/node/bo';
import type { NodeRenderOpts } from '@bika/types/node/vo';
import { type iString, iStringParse, type LocaleType } from 'basenext/i18n';
import type { Pagination, PaginationInfo } from 'basenext/pagination';
import { generateNanoID } from 'basenext/utils';
import _ from 'lodash';
import { ActionHandlersManager } from './action/action-handlers-manager';
import type { IAutomationRunInput } from './action/types';
import { ActionSO } from './action-so';
import { AutomationRunContextBuilder } from './automation-run-context-builder';
import { AutomationRunHistorySO } from './automation-run-history-so';
import { TriggerHandlerManager } from './trigger/trigger-handler-manager';
import { TriggerSO } from './trigger-so';
import {
  type ActionModel,
  type AutomationCreateInput,
  type AutomationCreateOperations,
  type AutomationCreateWithoutNodeInput,
  type AutomationModel,
  automationInclude,
  type DefaultAutomationModel,
  type IAutomationRunContext,
  type IAutomationRunOptions,
  type TriggerOperation,
} from './types';

export class AutomationSO extends NodeResourceSO {
  private _model: AutomationModel;

  private _triggers?: TriggerSO[];

  private _firstLevelActions?: ActionSO[];

  private constructor(po: AutomationModel, triggers?: TriggerSO[], actions?: ActionSO[]) {
    super();
    this._model = po;
    this._triggers = triggers;
    this._firstLevelActions = actions;
  }

  get resourceType(): NodeResourceType {
    return 'AUTOMATION';
  }

  override toNodeSO(): NodeSO {
    return NodeSO.initWithModel(this.model.node);
  }

  override async toBO(): Promise<Automation> {
    const [triggerSOs, actionSOs] = await Promise.all([
      this.getTriggers(),
      this.getFirstLevelActions(),
    ]);
    // state不需要导出
    const triggers = await Promise.all(
      triggerSOs.map((trigger: TriggerSO) => trigger.toBO({ withState: false })),
    );
    const actions = await Promise.all(
      actionSOs.map((action: ActionSO) => action.toBO({ withState: false })),
    );
    return {
      resourceType: 'AUTOMATION',
      name: this.name,
      description: this.description,
      triggers,
      actions,
      id: this.id,
      templateId: this.templateId,
      icon: this.toNodeSO().icon,
    };
  }

  override async validate(): Promise<boolean> {
    const vo = await this.toVO();
    if (vo.actions.length === 0) {
      return false;
    }
    if (vo.triggers.length === 0) {
      return false;
    }
    return vo.isVerified;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get name() {
    return this.model.name as iString;
  }

  get isActive() {
    return this.model.isActive;
  }

  getName(locale: LocaleType = 'en'): string {
    return iStringParse(this.name, locale);
  }

  get description() {
    return (this.model.description as iString) || undefined;
  }

  get templateId() {
    return this.model.templateId || undefined;
  }

  get spaceId() {
    return this.model.spaceId;
  }

  async getSpace(): Promise<SpaceSO> {
    return SpaceSO.init(this.spaceId);
  }

  static async init(automationId: string, ensure?: boolean): Promise<AutomationSO> {
    const automationPO = await db.prisma.automation.findUnique({
      where: { id: automationId },
      include: automationInclude,
    });
    if (!automationPO) {
      throw new ServerError(errors.common.not_found);
    }
    const automationSO = new AutomationSO(automationPO);
    if (!ensure) {
      return automationSO;
    }
    await automationSO.ensureSchedulers(); // 预加载
    return automationSO;
  }

  static async initWithModel(model: AutomationModel): Promise<AutomationSO> {
    return new AutomationSO(model);
  }

  /**
   * 创建自动化类型节点数据操作
   * @param userId user id
   * @param createParam create params
   * @param automationTemplate automation template
   */
  static async createAutomationOperationWithTemplate(
    user: UserSO,
    createParam: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      automationTemplate: Automation;
      isTemplateOperation?: boolean;
      unitId?: string;
    },
  ): Promise<AutomationCreateOperations> {
    const { automationId, automationInput, schedulerInputs, callbackFns } =
      await AutomationSO.buildAutomationCreateInput(user, createParam);
    const operations: PrismaPromise<DefaultAutomationModel | Prisma.BatchPayload>[] = [];
    const createAutomationOperation = db.prisma.automation.create({
      data: automationInput,
    });
    operations.push(createAutomationOperation);
    if (schedulerInputs.length > 0) {
      operations.push(SchedulerSO.createMany(schedulerInputs));
    }
    return { automationId, operations, callbackFns };
  }

  override async toTemplate(opts?: ToTemplateOptions): Promise<Automation> {
    const automation = _.omit(await this.toBO(), 'id') as Automation;
    if (!automation.templateId) {
      automation.templateId = this.id;
    }
    const actions = await this.getFirstLevelActions();
    const actionBOList: Action[] = [];
    for (const action of actions) {
      actionBOList.push(await action.toTemplate(opts));
    }
    const triggers = await this.getTriggers();
    const triggerBOList: Trigger[] = [];
    for (const trigger of triggers) {
      triggerBOList.push(trigger.toTemplate(opts));
    }
    return { ...automation, triggers: triggerBOList, actions: actionBOList };
  }

  override async export(opts?: ExportBOOptions): Promise<Automation> {
    const automation = await this.toBO();
    const actionBOList: Action[] = [];
    const triggerBOList: Trigger[] = [];
    const triggers = await this.getTriggers();
    for (const trigger of triggers) {
      triggerBOList.push(
        await trigger.exportBO({
          ...opts,
          resourceName: iStringParse(this.name, opts?.locale),
        }),
      );
    }
    const actions = await this.getFirstLevelActions();
    for (const action of actions) {
      actionBOList.push(
        await action.exportBO({
          ...opts,
          resourceName: iStringParse(this.name, opts?.locale),
        }),
      );
    }
    return { ...automation, triggers: triggerBOList, actions: actionBOList };
  }

  override async setTemplateId() {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = this.setTemplateIdWithIdOperation();
    const actions = await this.getFirstLevelActions();
    for (const action of actions) {
      operations.push(...action.setTemplateId());
    }
    const triggers = await this.getTriggers();
    for (const trigger of triggers) {
      operations.push(...trigger.setTemplateId());
    }
    return operations;
  }

  /**
   * 预加载，处理triggers，确保schedulers创建
   */
  async ensureSchedulers(): Promise<boolean> {
    if (!this.model.isActive) {
      return false;
    }
    // 判断是否需要创建scheduler?
    const triggers = await this.getTriggers();
    await Promise.allSettled(
      triggers.map((trigger: TriggerSO) => trigger.ensureSchedulerOfTrigger()),
    );
    return true;
  }

  async findSchedulers(): Promise<SchedulerSO[]> {
    // 获取所有triggers
    const triggers = await this.getTriggers();
    const triggerIds = triggers.filter((t) => t.withScheduler).map((t) => t.id);
    // 获取所有actions
    const actions = await this.getActions();
    const actionIds = actions.filter((a) => a.withScheduler).map((a) => a.id);
    return SchedulerSO.findSchedulersByRelation([...triggerIds, ...actionIds]);
  }

  /**
   * update database
   * @param userId user id
   * @param data update data
   */
  updateOperation(
    data: Pick<Prisma.AutomationUpdateInput, 'name' | 'description' | 'updatedBy' | 'node'>,
  ): PrismaPromise<DefaultAutomationModel> {
    return db.prisma.automation.update({
      where: { id: this.id },
      data,
    });
  }

  async activate() {
    // 校验自动化可运行次数, 如果没超量让其开启, 中途循环过程中如果超量,则会把状态改为false
    const space = await this.getSpace();
    const entitlement = await space.getEntitlement();
    await entitlement.checkUsageExceed({ feature: 'AUTOMATION_RUNS' });
    await db.prisma.automation.update({
      data: { isActive: true },
      where: { id: this.id },
    });
    this._model.isActive = true;
    await this.ensureSchedulers();
    EventSO.automation.onActivate(this);
  }

  async deactivate() {
    await db.prisma.automation.update({
      data: { isActive: false },
      where: { id: this.id },
    });
    this._model.isActive = false;
    const triggers = await this.getTriggers();
    const triggerIds = triggers.filter((t) => t.withScheduler).map((t) => t.id);
    await SchedulerSO.deleteByRelationIds(triggerIds, AutomationTriggerRelationType);
    EventSO.automation.onDeactivate(this);
  }

  async addTrigger(user: UserSO, trigger: Trigger): Promise<TriggerSO> {
    const triggerSO = await TriggerSO.createTrigger({
      user,
      automation: this,
      trigger,
    });
    EventSO.automation.onTriggerCreated(triggerSO);
    return triggerSO;
  }

  async getTriggers(useCache: boolean = true): Promise<TriggerSO[]> {
    if (this._triggers && useCache) {
      return this._triggers;
    }
    this._triggers = await TriggerSO.findByAutomationId(this.id);
    return this._triggers;
  }

  async getTriggerCount(useCache: boolean = true): Promise<number> {
    return (await this.getTriggers(useCache)).length;
  }

  async addAction(userId: string, action: Action, parentActionId?: string): Promise<ActionSO> {
    const getSameLevelActions = async () => {
      if (!parentActionId) {
        return this.getFirstLevelActions(false);
      }
      const parentAction = await ActionSO.init(parentActionId);
      // 检查 parentAction 是否在当前自动化中
      if (parentAction.automationId !== this.id) {
        throw new ServerError(errors.automation.action_not_in_automation);
      }
      // 拒绝嵌套超过2层
      if (parentAction.model.parentActionId) {
        throw new ServerError(errors.automation.action_nested_exceed);
      }
      return parentAction.getChildrenActions({ sort: true });
    };
    const actions = await getSameLevelActions();
    const preActionId = actions.length > 0 ? actions[actions.length - 1].id : undefined;
    // 创建action
    const actionSO = await ActionSO.createAction({
      userId,
      spaceId: this.model.spaceId,
      automationId: this.id,
      parentActionId,
      preActionId,
      action,
    });
    EventSO.automation.onActionCreated(actionSO);
    return actionSO;
  }

  async getFirstLevelActions(useCache: boolean = true): Promise<ActionSO[]> {
    if (this._firstLevelActions && useCache) {
      return this._firstLevelActions;
    }
    this._firstLevelActions = await ActionSO.findFirstLevelActions(this.id);
    return this._firstLevelActions;
  }

  async getActions(useCache: boolean = true): Promise<ActionSO[]> {
    const firstLevelActions = await this.getFirstLevelActions(useCache);
    const allNestedActions = await Promise.all(
      firstLevelActions.map((i) => i.getChildrenActions({ useCache })),
    );
    return firstLevelActions.concat(allNestedActions.flat());
  }

  async getActionCount(useCache: boolean = true): Promise<number> {
    return (await this.getActions(useCache)).length;
  }

  /**
   * 获取自动化是否可运行
   */
  async canRun(): Promise<boolean> {
    if (!this.model.isActive) {
      return false;
    }
    // 判断空间站是否活跃
    const isSpaceActive = await SpaceSO.isSpaceActive(this.spaceId);
    // 非活跃空间站，自动停止自动化
    if (!isSpaceActive) {
      Logger.warn(`Space ${this.spaceId} not active, automation ${this.id} will be deactivated.`);
      await this.deactivate();
    }
    return isSpaceActive;
  }

  async getRunHistories(
    q?: { startDate: Date; endDate: Date },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: AutomationRunHistorySO[] }> {
    return AutomationRunHistorySO.find({ automationId: this.id, date: q && { ...q } }, pagination);
  }

  async fetchOutputObjectForVariables(user?: UserSO): Promise<AutomationVariablesVO> {
    const templateNodeId = await this.toNodeSO().findTemplateFolderNodeId();
    const automationOpts = { locale: user?.locale, templateNodeId };
    // trigger fake results
    const triggerFakeResults = [];
    const triggers = await this.getTriggers();
    for (const trigger of triggers) {
      try {
        const output = await trigger.fetchTriggerOutputMaybeFake(user, automationOpts);
        triggerFakeResults.push({
          id: trigger.id,
          success: true,
          output,
          templateId: trigger.templateId,
          type: trigger.type,
          description: trigger.description,
        });
      } catch (error: unknown) {
        triggerFakeResults.push({
          id: trigger.id,
          success: false,
          error: getUnknownErrorMessage(error),
          templateId: trigger.templateId,
          type: trigger.type,
          description: trigger.description,
        });
      }
    }
    // action fake results
    const actions = await this.getFirstLevelActions();
    const getActionFakeResult = async (action: ActionSO): Promise<ActionFakeResultVO> => {
      let actionTestResult: { success: true; output: unknown } | { success: false; error: string };
      try {
        const output = await action.fetchActionOutputMaybeFake(automationOpts);
        actionTestResult = { success: true, output };
      } catch (error: unknown) {
        actionTestResult = { success: false, error: getUnknownErrorMessage(error) };
      }
      // nested actions
      const nestedActions = await action.getChildrenActions({ sort: true });
      const nestedActionFakeResults = await Promise.all(
        nestedActions.map((i) => getActionFakeResult(i)),
      );
      return {
        ...actionTestResult,
        id: action.id,
        templateId: action.templateId,
        type: action.type,
        description: action.description,
        actions: nestedActionFakeResults,
      };
    };
    const actionFakeResults = await Promise.all(
      actions.map((action) => getActionFakeResult(action)),
    );
    // automation variables
    return {
      triggers: triggerFakeResults,
      actions: actionFakeResults,
    };
  }

  /**
   * 主动取消（中断）延迟状态的自动化继续运行
   *
   * @param runHistory  运行历史
   */
  static async cancelDelayAutomation(runHistory: AutomationRunHistorySO, userId?: string) {
    if (runHistory.status !== 'DELAY') {
      throw new Error('Only delay status can be canceled.');
    }
    const { actions } = runHistory.data;
    if (actions.length === 0) {
      throw new Error('No actions to cancel.');
    }
    const delayAction = actions[actions.length - 1];
    const schedulers = await SchedulerSO.findSchedulersByRelation(
      [delayAction.id],
      AutomationActionRelationType,
      {
        path: ['automationRunHistoryId'],
        equals: runHistory.id,
      },
    );
    // 删除 action scheduler
    if (schedulers.length > 0) {
      await SchedulerSO.deleteByIds(schedulers.map((i) => i.id));
    }
    // 更新运行结果状态
    await runHistory.updateRunResult({ userId, status: 'CANCELLED' });
  }

  async runWithoutTrigger(userId?: string): Promise<IAutomationRunContext> {
    return this.run([], { userId, manual: true });
  }

  async run(
    triggerRunDetailVOs: TriggerRunDetailVO[],
    optional?: IAutomationRunOptions,
  ): Promise<IAutomationRunContext> {
    const { userId, manual } = optional || {};

    // const space = await this.getSpace();
    // const entitlement = await space.getEntitlement();
    // await entitlement.checkUsageExceed({ feature: 'AUTOMATION_RUNS' });

    const buildSortedTriggers = async () => {
      if (triggerRunDetailVOs.length <= 1) {
        return triggerRunDetailVOs;
      }
      const triggers = await this.getTriggers();
      const triggerIdToRunDetailVOMap = triggerRunDetailVOs.reduce((map, result) => {
        map.set(result.id, _.omit(result, 'withState') as TriggerRunDetailVO); // 运行历史不需要保存state(目前只有testOutput)
        return map;
      }, new Map<string, TriggerRunDetailVO>());
      return triggers.reduce<TriggerRunDetailVO[]>((acc, trigger) => {
        const vo = triggerIdToRunDetailVOMap.get(trigger.id);
        if (vo) {
          acc.push(vo);
        }
        return acc;
      }, [] as TriggerRunDetailVO[]);
    };
    const sortedTriggerRunDetailVOs = await buildSortedTriggers();

    // 创建开始运行状态的记录
    const runHistory = await AutomationRunHistorySO.createRunningRecord(
      this.spaceId,
      this.id,
      sortedTriggerRunDetailVOs,
      manual,
      userId,
    );
    const builder = await AutomationRunContextBuilder.initRunContextBuilder(this, runHistory);
    // run all actions
    const actions = await this.getFirstLevelActions(false);
    return this.doActions(actions, builder, runHistory);
  }

  private async doActions(
    actions: ActionSO[],
    builder: AutomationRunContextBuilder,
    runHistory: AutomationRunHistorySO,
  ) {
    // 若 automation 是从模板生成的实例，找到模板所在的模板节点文件夹
    const templateNodeId = await this.toNodeSO().findTemplateFolderNodeId();
    // build automation params
    const userId = runHistory.createdBy || this.model.createdBy;
    const user = userId ? await UserSO.init(userId) : undefined;
    const params: IAutomationRunInput = {
      spaceId: this.spaceId,
      automationId: this.id,
      automationRunHistoryId: runHistory.id,
      templateNodeId,
      // 触发者用户ID
      userId: runHistory.createdBy,
      // 触发者不存在时，使用自动化创建者的时区/语言
      locale: user?.locale,
      timezone: user?.timeZone,
    };
    const context = builder.context;
    const actionRunDetailVOs: ActionRunDetailVO[] = runHistory.data.actions;
    let runResultStatus: AutomationRunHistoryStatus = 'SUCCESS';
    for (const action of actions) {
      try {
        const {
          action: bo,
          output,
          isDelay,
          interrupt,
          itemActionRunResults,
        } = await action.run(context, params);
        builder.appendAction({ id: action.id, templateId: action.templateId }, output);
        actionRunDetailVOs.push({
          id: action.id,
          ...bo,
          state: undefined,
          output,
          itemActionRunResults,
        });
        // 如果有延迟，直接结束
        if (isDelay) {
          runResultStatus = 'DELAY';
          break;
        }
        // 如果有中断，直接结束
        if (interrupt) {
          break;
        }
      } catch (error: unknown) {
        const errorOutput = { error: getUnknownErrorMessage(error) };
        runResultStatus = 'FAILURE';
        actionRunDetailVOs.push({
          id: action.id,
          ...(await action.toBO({ withState: false })),
          output: errorOutput,
        });
        builder.appendAction({ id: action.id, templateId: action.templateId }, errorOutput);
        // 触发事件
        EventSO.automation.onRunFailed(this, {
          actionId: action.id,
          runHistoryId: runHistory.id,
          errorMessage: errorOutput.error,
        });
        // 默认策略 STOP_ON_ERROR，报错后续action不再执行，直接结束。继续执行 CONTINUE_ON_ERROR 待定
        break;
      }
    }
    Logger.debug(`automation run done. context: ${context}`);
    // update run result
    await runHistory.updateRunResult({ status: runResultStatus, actions: actionRunDetailVOs });
    return context;
  }

  async runAfterDelay(delayActionId: string, runHistoryId: string) {
    const runHistory = await AutomationRunHistorySO.init(runHistoryId);

    // 检查自动化是否可运行，不可运行直接结束
    const automationIsActive = await this.canRun();
    if (!automationIsActive) {
      await runHistory.updateRunResult({ status: 'CANCELLED' });
      return;
    }

    const actions = await this.getFirstLevelActions(false);
    const delayActionIndex = actions.findIndex((action) => action.id === delayActionId);
    // Delay Action 不存在，不知道延续哪个位置的Action继续执行，直接结束
    // Delay Action 是最后一个Action，直接结束
    if (delayActionIndex === -1 || delayActionIndex === actions.length - 1) {
      await runHistory.updateRunResult({ status: 'SUCCESS' });
      return;
    }
    // 构建运行上下文
    const builder = await AutomationRunContextBuilder.initDelayRunContextBuilder(this, runHistory);
    // 运行 Delay Action 之后的Actions
    const actionsAfterDelay = actions.slice(delayActionIndex + 1);
    await this.doActions(actionsAfterDelay, builder, runHistory);
  }

  async toVO(opts?: NodeRenderOpts): Promise<AutomationVO> {
    const templateNodeId = await this.toNodeSO().findTemplateFolderNodeId();
    const automationOpts = { ...opts, templateNodeId };
    // 获取所有actions
    const actionSOs = await this.getFirstLevelActions();
    const actions = await Promise.all(
      actionSOs.map((action: ActionSO) => action.toVO(automationOpts)),
    );
    const actionsAllVerified = actions.every((action) => action.isVerified);
    // 获取所有triggers
    const triggerSOs = await this.getTriggers();
    const triggers = await Promise.all(
      triggerSOs.map((trigger: TriggerSO) => trigger.toVO(automationOpts)),
    );
    const triggersAllVerified = triggers.every((trigger) => trigger.isVerified);
    return {
      id: this.id,
      name: this.getName(opts?.locale),
      description: iStringParse(this.description, opts?.locale),
      isActive: this.model.isActive,
      actions,
      triggers,
      isVerified: triggersAllVerified && actionsAllVerified,
    };
  }

  async update(userId: string, param: AutomationUpdateBO): Promise<AutomationSO> {
    const operations = await this.updateWithNodeInput(userId, param);
    await db.prisma.$transaction(operations);
    return AutomationSO.init(this.id);
  }

  async updateWithNodeInput(
    userId: string,
    param: AutomationUpdateBO,
  ): Promise<PrismaPromise<AutomationModel | ActionModel>[]> {
    const operations: PrismaPromise<AutomationModel | ActionModel>[] = [];
    operations.push(
      db.prisma.automation.update({
        where: {
          id: this.id,
        },
        data: {
          name: param.name,
          description: param.description,
          updatedBy: userId,
          node: {
            update: {
              name: param.name,
              description: param.description,
              updatedBy: userId,
            },
          },
        },
        include: {
          node: true,
        },
      }),
    );
    if (param.actions) {
      operations.push(...(await ActionSO.updateSort(userId, param.actions)));
    }
    return operations;
  }

  /**
   * TODO: 优化方法返回为新BO
   */
  override relationInstanceId(automation: Automation, opts: IRelationIdOpts): boolean {
    // triggers
    const triggers: Trigger[] = [];
    for (const trigger of automation.triggers) {
      const triggerHandler = TriggerHandlerManager.getTriggerHandler(trigger.triggerType);
      if (opts.convertToInstanceId) {
        triggers.push(triggerHandler.installTemplateBO(trigger, opts.convertToInstanceId));
      } else if (opts.replaceInstanceId) {
        triggers.push(triggerHandler.importBO(trigger, opts.replaceInstanceId));
      } else if (opts.convertToTemplateId) {
        triggers.push(triggerHandler.toTemplateBO(trigger));
      }
    }
    automation.triggers = triggers;
    // actions
    const relationActionInput = (data: Action) => {
      const handler = ActionHandlersManager.getAction(data.actionType);
      if (opts.convertToInstanceId) {
        return handler.installTemplateBO(data, opts.convertToInstanceId, {
          automationTemplateId: automation.templateId,
        });
      }
      if (opts.replaceInstanceId) {
        return handler.importBO(data, opts.replaceInstanceId);
      }
      if (opts.convertToTemplateId) {
        return handler.toTemplateBO(data, opts.convertToTemplateId);
      }
      return data;
    };
    const actions: Action[] = [];
    for (const action of automation.actions) {
      const newAction = relationActionInput(action);
      // nested actions
      if ('actions' in newAction && newAction.actions) {
        const nestedActions: Action[] = [];
        for (const subAction of newAction.actions) {
          nestedActions.push(relationActionInput(subAction));
        }
        newAction.actions = nestedActions;
      }
      actions.push(newAction);
    }
    automation.actions = actions;
    return true;
  }

  static async boToCreateInput(
    spaceId: string,
    user: UserSO,
    data: AutomationCreateBO,
  ): Promise<AutomationCreateWithoutNodeInput> {
    const { resourceType: nodeType, name, description, templateId } = data;
    if ($Enums.NodeResourceType.AUTOMATION !== nodeType) {
      return {};
    }
    // build triggers input
    const { triggerInputs, schedulerInputs, triggerCallbackFns } = data.triggers
      ? await TriggerSO.buildTriggerCreateManyInputNotConnectAutomation({
          user,
          spaceId,
          triggers: data.triggers,
          isActive: data.status === 'ACTIVE',
          isTemplateOperation: !!data.templateId,
        })
      : { triggerInputs: undefined, schedulerInputs: undefined, triggerCallbackFns: undefined };
    const { actionsInputs, callbackFns: actionCallbackFns } = data.actions
      ? ActionSO.buildActionCreateManyInputNotConnectAutomation({
          userId: user.id,
          spaceId,
          actions: data.actions,
        })
      : { actionsInputs: undefined, callbackFns: undefined };
    const automationInput: Prisma.AutomationCreateNestedOneWithoutNodeInput = {
      create: {
        spaceId,
        name,
        description,
        templateId,
        createdBy: user.id,
        updatedBy: user.id,
        isActive: data.status === 'ACTIVE',
        actions: data.actions // todo parentActionId
          ? {
              create: actionsInputs,
            }
          : undefined,
        triggers: data.triggers
          ? {
              create: triggerInputs,
            }
          : undefined,
      },
    };
    return {
      automationInput,
      schedulerInputs,
      callbackFns: [...(triggerCallbackFns || []), ...(actionCallbackFns || [])],
    };
  }

  /**
   * 构造自动化类型节点创建数据
   * @param userId user idd
   * @param params automation create params
   * @param automation automation template
   */
  private static async buildAutomationCreateInput(
    user: UserSO,
    params: {
      spaceId: string;
      parentId: string;
      preNodeId?: string;
      nextNodeId?: string;
      automationTemplate: Automation;
      isTemplateOperation?: boolean;
      unitId?: string;
    },
  ): Promise<AutomationCreateInput> {
    const {
      spaceId,
      automationTemplate: automation,
      parentId,
      preNodeId,
      nextNodeId,
      isTemplateOperation,
      unitId,
    } = params;
    // 创建automation
    const automationId = automation.id || generateNanoID('ato');

    const automationCreateInput: Prisma.AutomationCreateInput = {
      spaceId,
      templateId: automation.templateId,
      name: automation.name,
      description: automation.description,
      isActive: automation.status === 'ACTIVE',
      createdBy: user.id,
      updatedBy: user.id,
      node: {
        create: {
          id: automationId,
          templateId: automation.templateId,
          name: automation.name,
          description: automation.description,
          icon: automation.icon,
          type: 'AUTOMATION',
          createdBy: user.id,
          updatedBy: user.id,
          unit: unitId ? { connect: { id: unitId } } : undefined,
          space: {
            connect: { id: spaceId },
          },
          parent: {
            connect: { id: parentId },
          },
          preNode: {
            connect: preNodeId ? { id: preNodeId } : undefined,
          },
          nextNode: {
            connect: nextNodeId ? { id: nextNodeId } : undefined,
          },
        },
      },
    };

    // build triggers input
    const { triggerInputs, schedulerInputs, triggerCallbackFns } =
      await TriggerSO.buildTriggerCreateManyInputNotConnectAutomation({
        user,
        spaceId,
        triggers: automation.triggers,
        isActive: automationCreateInput.isActive,
        isTemplateOperation,
      });
    automationCreateInput.triggers = {
      createMany: {
        data: triggerInputs,
      },
    };

    // build actions input
    const { actionsInputs, callbackFns: actionCallbackFns } =
      ActionSO.buildActionCreateManyInputNotConnectAutomation({
        userId: user.id,
        spaceId,
        actions: automation.actions,
      });
    automationCreateInput.actions = {
      createMany: {
        data: actionsInputs,
      },
    };

    return {
      automationId,
      automationInput: automationCreateInput,
      schedulerInputs,
      callbackFns: [...(triggerCallbackFns || []), ...(actionCallbackFns || [])],
    };
  }

  async updateActionsOperation(
    userId: string,
    upgradeActions: Action[],
  ): Promise<PrismaPromise<ActionModel>[]> {
    const operations: PrismaPromise<ActionModel>[] = [];
    const { id: automationId, spaceId } = this.model;
    const beforeActions = await this.getFirstLevelActions();
    // 最后一个action的id作为前置id往下叠加新action
    let preActionId: string | undefined =
      beforeActions.length > 0 ? beforeActions[beforeActions.length - 1].id : undefined;
    upgradeActions.forEach((upgradeAction) => {
      // 设置了唯一键
      if (upgradeAction.templateId) {
        const matchAction = beforeActions.find(
          (action) => action.templateId === upgradeAction.templateId,
        );
        // 且匹配到了已存在同一唯一键的Action，执行更新操作
        if (matchAction) {
          operations.push(matchAction.updateActionOperation(userId, upgradeAction));
          return;
        }
      }
      // 没有设置唯一键或未匹配到，直接创建action
      const { actionId, operation } = ActionSO.createActionOperation({
        userId,
        spaceId,
        automationId,
        preActionId,
        action: upgradeAction,
      });
      operations.push(operation);
      preActionId = actionId;
    });
    // console.log(`自动化 ${automationSO.id} 的任务更改数量: ${actionUpsertOperation.length}`);
    return operations;
  }

  async updateTriggersOperation(
    userId: string,
    upgradeTriggers: Trigger[],
    isTemplateOperation?: boolean,
  ): Promise<{ triggerOperations: TriggerOperation[]; callbackFns: (() => Promise<void>)[] }> {
    const triggerUpsertOperations: TriggerOperation[] = [];
    const callbackFns: (() => Promise<void>)[] = [];
    const { id: automationId, spaceId, isActive } = this.model;
    const user = await UserSO.init(userId);
    const beforeTriggers = await this.getTriggers();
    // 最后一个视图的id作为前置视图id往下叠加新视图
    let preTriggerId: string | undefined =
      beforeTriggers.length > 0 ? beforeTriggers[beforeTriggers.length - 1].id : undefined;
    for (const upgradeTrigger of upgradeTriggers) {
      // 设置了唯一键
      if (upgradeTrigger.templateId) {
        const matchTrigger = beforeTriggers.find(
          (trigger) => trigger.templateId === upgradeTrigger.templateId,
        );
        // 且匹配到了已存在同一唯一键的触发器，执行更新操作
        if (matchTrigger) {
          // merge old trigger input with the updated trigger input, prevent update the trigger ignore data
          const { operations, templateCallbackFn } =
            await matchTrigger.buildUpdateTriggerOperations(user, upgradeTrigger, true);
          triggerUpsertOperations.push(...operations);
          if (templateCallbackFn) {
            callbackFns.push(templateCallbackFn);
          }
          continue;
        }
      }
      // 没有设置唯一键或未匹配到，直接创建触发器
      const { triggerId, operations, templateCallbackFn } = await TriggerSO.createTriggerOperation({
        user,
        spaceId,
        automationId,
        isActive,
        preTriggerId,
        trigger: upgradeTrigger,
        isTemplateOperation,
      });
      triggerUpsertOperations.push(...operations);
      if (templateCallbackFn) {
        callbackFns.push(templateCallbackFn);
      }
      preTriggerId = triggerId;
    }
    // console.log(`自动化 ${automationSO.id} 的触发器更改数量: ${triggerUpsertOperation.length}`);
    return { triggerOperations: triggerUpsertOperations, callbackFns };
  }

  async updateNodeState(): Promise<void> {
    const node = this.toNodeSO();
    const state: NodeStateBO[] = node.state.filter(
      (i) => i.state !== 'ERROR' && i.state !== 'STOP' && i.state !== 'RUNNING',
    );
    const validate = await this.validate();
    let newState: NodeStateBO = {
      state: 'STOP',
    };
    if (!validate) {
      newState = {
        state: 'ERROR',
        message: '',
      };
    } else if (this.isActive) {
      newState = {
        state: 'RUNNING',
      };
    }
    state.unshift(newState);
    await NodeSO.updateNodeState(this.id, state);
  }

  private setTemplateIdWithIdOperation() {
    const operations: PrismaPromise<Prisma.BatchPayload>[] = [];
    if (!this.templateId) {
      operations.push(
        ...[
          db.prisma.automation.updateMany({
            where: { id: this.id },
            data: { templateId: this.id },
          }),
          db.prisma.node.updateMany({
            where: { id: this.id },
            data: { templateId: this.id },
          }),
        ],
      );
    }
    return operations;
  }
}
